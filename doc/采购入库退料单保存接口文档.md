## 采购入库退料单保存接口

### 接口描述：

- 广交云.【采购入库单】.修改操作.审核后 **推送** 金蝶云·星空旗舰版.【采购入库单】
- 而广交云.【采购入库单】.新增操作.需调用 **[6.1.下推并保存接口](#_下推并保存接口) 下推** 生成金蝶云·星空旗舰版.【采购入库单】，然后获取返回的目标单据id和目标单据分录id，再调用本接口进行保存操作

- 广交云.【采退出库单】.修改操作.审核后 **推送** 金蝶云·星空旗舰版.【采购退料单】
- 而广交云.【采退出库单】.新增操作.需调用 **[6.1.下推并保存接口](#_下推并保存接口) 下推** 生成金蝶云·星空旗舰版.【采购退料单】，然后获取返回的目标单据id和目标单据分录id，再调用本接口进行保存操作

采购入库单与采购退料单保存接口相同。

从采购入库单转为采购退料单需要改以下字段：

1、单据类型改为im_PurInBill_STD_BT_S_R

1.  业务类型编码和库存事务编码都改为退料相关如，1101
2.  returnmaterialtype 退料类型必填

采购入库单 单据类型编码：im_PurInBill_STD_BT_S

采购退料单 单据类型编码：im_PurInBill_STD_BT_S_R

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/im_purinbill/savePruinBill

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/im_purinbill/savePruinBill](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| org_number | String | 是   | 库存组织.编码 | 1   | 固定值，传"1011" |
| billno | String | 否   | 单据编号 | 1   | "5Xrbr" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "CGRK-20250505-005"，传广交云单号 |
| &nbsp;      gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
| billtype_number | String | 是   | 单据类型.编码（<br><br>采购入库单-：im_PurInBill_STD_BT_S<br><br>采购退料单-：im_PurInBill_STD_BT_S_R<br><br>） | 1   | 采购入库单-：im_PurInBill_STD_BT_S<br><br>采购退料单-：im_PurInBill_STD_BT_S_R |
| biztime | Date | 是   | 业务日期 | 1   | "2022-07-04" |
| exratedate | Date | 是   | 汇率日期 | 1   | "2022-07-04"，同业务日期 |
| exratetable_number | String | 是   | 汇率表.编码 | 1   | 固定值，传"ERT-01" |
| exchangerate | Decimal | 是   | 汇率  | 1   | 固定值，传1 |
| paymode | String | 是   | 付款方式 | 1   | "CREDIT" |
| bizorg_number | String | 是   | 采购组织.编码 | 1   | 固定值，传"1011" |
| biztype_number | String | 是   | 业务类型.编码(<br><br>110-物料类采购<br><br>1101-物料类采购退货<br><br>) | 1   | 如为采购入库单时传"110"，<br><br>如为采购退料单时传"1101" |
| invscheme_number | String | 是   | 库存事务.编码(<br><br>110-物料类采购<br><br>1101-普通采购退、补货<br><br>) | 1   | 如为采购入库单时传"110"，<br><br>如为采购退料单时传"1101" |
| supplier_number | String | 是   | 供应商.编码 | 1   | "f4VWa" |
| currency_number | String | 是   | 本位币.货币代码 | 1   | 固定值，传"CNY"(人民币) |
| settlecurrency_number | String | 是   | 结算币别.货币代码 | 1   | 固定值，传"CNY"(人民币) |
| paycondition_number | String | 是   | 付款条件.编号(<br><br>对照 《付款条件.xlsx》) | 1   | 传对应的付款条件.编号 |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| id  | String | 否   | 物料明细.id | 2   | 修改时需传 |
| returnmaterialtype | String | 否   | 物料明细.退料类型 0:, 1:退料, 2:退补料 | 2   | 如为采购退料单时传"1" |
| unit_number | String | 是   | 库存单位.编码 | 2   | "pcs" |
| warehouse_number | String | 是   | 仓库.编码 | 2   | "DzU3c" |
| invstatus_number | String | 是   | 入库库存状态.编码<br><br>（110-可用） | 2   | 固定值，传"110" |
| ownertype | String | 是   | 物料明细.入库货主类型 bd_supplier:供应商, bd_customer:客户, bos_org:核算组织 | 2   | 固定值，传"bos_org" |
| owner_number | String | 是   | 入库货主.编码 | 2   | 固定值，传"1011" |
| keepertype | String | 是   | 物料明细.入库保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| keeper_number | String | 是   | 入库保管者.编码 | 2   | 固定值，传"1011" |
| invtype_number | String | 是   | 入库库存类型.编码（110-普通） | 2   | 固定值，传"110" |
| linetype_number | String | 是   | 行类型.编码(010-物资) | 2   | 固定值，传"010" |
| qty | Decimal | 是   | 物料明细.数量 | 2   | "659.43" |
| material_number | String | 是   | 物料编码.编码 | 2   | "DxD77"，广交产品编码 |
| baseunit_number | String | 是   | 基本单位.编码 | 2   | "HV5zC"，同库存单位.编码 |
| baseqty | Decimal | 是   | 物料明细.基本数量 | 2   | "372.55"，同物料明细.数量 |
| &nbsp; taxrateid_number | String | 否   | 税率.编码 | 2   | "V5"，广交云做映射，传对应编码 |
| &nbsp; taxamount | Decimal | 否   | 物料明细.税额 | 2   | "0.9" |
| &nbsp; amountandtax | Decimal | 否   | 物料明细.价税合计 | 2   | "975.00" |
| &nbsp; amount | Decimal | 否   | 物料明细.金额 | 2   | "135.46" |
| &nbsp; ispresent | Boolean | 否   | 物料明细.赠品 | 2   | 默认为false，金额为0传true |
| discounttype | String | 是   | 物料明细.折扣方式 A:折扣率(%), NULL:无, B:单位折扣额 | 2   | "NULL" |
| &nbsp; discountrate | Decimal | 否   | 物料明细.单位折扣(率) | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; discountamount | Decimal | 否   | 物料明细.折扣额 | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; entrycomment | String | 否   | 物料明细.备注 | 2   | "备注文本" |
| &nbsp; project_number | String | 否   | 项目编码.项目编码 | 2   | "IHUsI" |
| &nbsp; producedate | Date | 否   | 物料明细.生产日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| &nbsp; expirydate | Date | 否   | 物料明细.到期日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| lotnumber | String | 否   | 物料明细.批号 | 2   | "IHUsI"，对应广交云批次，物料启用批号时必传 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data": \[

&nbsp;       {

&nbsp;           "billno": "CGRK-250729-000003",

&nbsp;           "gjwl_thirdparty_billno": "CGRK-250729-000003",

&nbsp;           "biztime": "2025-07-29",

&nbsp;           "comment": "备注",

&nbsp;           "bookdate": "2025-07-29",

&nbsp;           "istax": true,

&nbsp;           "exratedate": "2025-07-29",

&nbsp;           "paymode": "CREDIT",

&nbsp;           "org_number": "1011",

&nbsp;           "billtype_number": "im_PurInBill_STD_BT_S",

&nbsp;           "bizorg_number": "1011",

&nbsp;           "biztype_number": "110",

&nbsp;           "invscheme_number": "110",

&nbsp;           "paycondition_number": "FKTJ-1001_SYS",

&nbsp;           "settlecurrency_number": "CNY",

&nbsp;           "supplier_number": "GYS1115016",

&nbsp;           "exratetable_number": "ERT-01",

&nbsp;           "billentry": \[

&nbsp;               {

&nbsp;                   "taxrateid_number": "V5",

&nbsp;                   "remark": "备注文本",

&nbsp;                   "ispresent": false,

&nbsp;                   "unit_number": "pcs",

&nbsp;                   "baseunit_number": "pcs",

&nbsp;                   "price": "9.52",

&nbsp;                   "amount": "19.05",

&nbsp;                   "taxamount": "0.95",

&nbsp;                   "linetype_number": "010",

&nbsp;                   "priceandtax": "10.00",

&nbsp;                   "discounttype": "B",

&nbsp;                   "discountrate": "2.00",

&nbsp;                   "discountamount": "4.00",

&nbsp;                   "warehouse_number": "CK-001",

&nbsp;                   "material_number": "Item-*********",

&nbsp;                   "qty": "2",

&nbsp;                   "baseqty": "2",

&nbsp;                   "keepertype": "bos_org",

&nbsp;                   "keeper_number": "1011",

&nbsp;                   "ownertype": "bos_org",

&nbsp;                   "owner_number": "1011",

&nbsp;                   "invstatus_number": "110",

&nbsp;                   "invtype_number": "110",

&nbsp;                   "entrycomment": "明细备注",

&nbsp;                   "project_number": "KD-P-ALL_SYS",

&nbsp;                   "producedate": "2025-07-29",

&nbsp;                   "expirydate": "2029-07-29",

&nbsp;                   "lotnumber": "123",

&nbsp;                   "gjwl_product_lotno": "123"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| id  | "2274145548736921600" | String | 2   | 返回单据Id |
| number | "CGRK-250804-000001" | String | 2   | 返回单据编号 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data": {

"failCount": "0",

"result": \[

{

"billIndex": 0,

"billStatus": true,

"errors": \[\],

"id": "2274145548736921600",

"keys": {

"billno": "CGRK-250804-000001"

},

"number": "CGRK-250804-000001",

"type": "Add"

}

\],

"successCount": "1"

},

"errorCode": "0",

"message": null,

"status": true

}
