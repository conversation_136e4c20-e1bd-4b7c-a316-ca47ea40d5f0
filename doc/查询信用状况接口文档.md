## 信用状况表接口

### 接口描述：

- 广交云.【销售订单】 **调用** 金蝶云·星空旗舰版.【信用状况表接口】查询客户信用状况。

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/ccm/credit/getCreditBalance

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/ccm/credit/getCreditBalance](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**[调用方式](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)**[：HTTP调用](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**[请求方式](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)**[：POST](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**[请求类型](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)**[：Content-Type:application/json](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**[请求Header参数：(和 请求Header参数的一致)](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)**

**[请求Body参数：](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **[参数名](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)** | **[参数类型](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)** | **[是否必填](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)** | **[描述说明](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)** | **[层级](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)** | **[参数值](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)** |
| [currencynumber](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [String](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [否](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [币别编码(值为具体的币别编码)](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | ["CNY"](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |
| [orgscope](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [String](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [否](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [控制组织范围(输入控制范围的编码，如：组织范围：SINGLE；集团范围GLOBAL）](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [固定值，传"SINGLE"](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |
| [orgnumberset](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [Set&lt;String&gt;](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [否](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [授信组织编码集合（输入授信组织对应的业务单元编码）](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [\["1011"\]](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |
| [dimensionnumber](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [String](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [是](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [信控维度编码（如：客户：CUSTOMER；供应商：SUPPLIER）](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [固定值，传"CUSTOMER"](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |
| [roletype0](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [String](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [否](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [维度成员类型0:信用维度成员类型标识（输入维度成员对应的业务对象编码，可在维度成员列表查找。如客户：bd_customer，客户统一码：ccm_cusunicode）](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [固定值，传"bd_customer"](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |
| [rolenumberset0](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [Set&lt;String&gt;](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [否](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [维度成员0:信用维度对象编码集合（输入维度成员值对应的编码，如果信控维度是客户，则录入具体的客户编码）](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [客户编码](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |
| [schemenumber](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [String](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [否](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [信用控制方案编码](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [1](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) | [固定值，传"XKFA-SYS-001"](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface) |

**请求参数示例（沙箱环境）:**

{

"currencynumber":"CNY",

"orgscope":"SINGLE",

"orgnumberset":\[

"1011"

\],

"dimensionnumber":"CUSTOMER",

"roletype0":"bd_customer",

"rolenumberset0":\[

"C001","C002"

\],

"schemenumber":"XKFA-SYS-001"

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| status | true | boolean | 1   | 状态：true - 成功, false - 失败 |
| errorCode | ""  | String | 1   | 错误代码 |
| message | ""  | String | 1   | 错误信息 |
| data | "\[\]" | Array&lt;Map&gt; | 1   | 返回结果详细信息 |
| archiveidSet | 1726623948254 | Set&lt;Long&gt; | 2   | 信用档案ID |
| orgscope | "z0dCW" | String | 2   | 控制组织范围 |
| archiveorgnumberlist | "7Qk0q" | List&lt;String&gt; | 2   | 授信组织 |
| archiveobjectmap | \-  | Map&lt;String, String&gt; | 2   | 信用对象 |
| &nbsp;$key$ | "E8oTg" | String | 3   | $key$ |
| $value$ | "KEonM" | String | 3   | $value$ |
| &nbsp; schemenumber | "eYWyT" | String | 2   | 信用控制方案 |
| &nbsp;dimensionnumber | "7i6e5" | String | 2   | 信控维度 |
| &nbsp; currencynumber | "Zp4ri" | String | 2   | 币别  |
| &nbsp; singlecurcontrol | FALSE | boolean | 2   | 币别隔离 |
| &nbsp; quotaamount | 33.4335 | BigDecimal | 2   | 信用额度 |
| &nbsp; tempamount | 98.1846 | BigDecimal | 2   | 临时信用额度 |
| &nbsp; occupyamount | 82.6958 | BigDecimal | 2   | 实际占用额度 |
| &nbsp; balance | 9.393 | BigDecimal | 2   | 可用额度 |
| &nbsp; quotaoverdays | 12.8879 | BigDecimal | 2   | 信用天数 |
| &nbsp; tempoverdays | 96.0501 | BigDecimal | 2   | 临时信用天数 |
| &nbsp; actualoverdays | 64.6468 | BigDecimal | 2   | 逾期天数 |
| &nbsp; overdaysbal | 90.2049 | BigDecimal | 2   | 超标逾期天数 |
| &nbsp; quotaoveramount | 51.9352 | BigDecimal | 2   | 允许逾期额度 |
| &nbsp; tempoveramount | 41.1429 | BigDecimal | 2   | 临时逾期额度 |
| &nbsp; actualoveramount | 10.0609 | BigDecimal | 2   | 实际逾期额度 |
| &nbsp; overamountbal | 2.8151 | BigDecimal | 2   | 超标逾期金额 |
| &nbsp; exceed | FALSE | boolean | 2   | 是否超标 |
| &nbsp; grade | "V4ZJw" | String | 2   | 信用等级 |
| &nbsp; quoarchivemap | \-  | HashMap&lt;String, Long&gt; | 2   | 额度类型档案ID |
| &nbsp;   $key$ | "hcCK7" | String | 3   | $key$ |
| &nbsp;   $value$ | "1726623948282" | Long | 3   | $value$ |
| &nbsp; exratetablenumber | "s6Z0Q" | String | 2   | 汇率表 |

**返回参数示例:**

{

"data":\[

{

"archiveidSet":\[

"1726623948254"

\],

"currencynumber":"Zp4ri",

"tempoverdays":96.0501,

"dimensionnumber":"7i6e5",

"singlecurcontrol":false,

"actualoverdays":64.6468,

"exratetablenumber":"s6Z0Q",

"archiveobjectmap":{

"$value$":"KEonM",

"$key$":"E8oTg"

},

"schemenumber":"eYWyT",

"quotaoveramount":51.9352,

"overamountbal":2.8151,

"quoarchivemap":{

"$value$":"1726623948282",

"$key$":"hcCK7"

},

"occupyamount":82.6958,

"balance":9.3930,

"quotaoverdays":12.8879,

"tempamount":98.1846,

"exceed":false,

"grade":"V4ZJw",

"archiveorgnumberlist":\[

"7Qk0q"

\],

"actualoveramount":10.0609,

"tempoveramount":41.1429,

"quotaamount":33.4335,

"orgscope":"z0dCW",

"overdaysbal":90.2049

}

\],

"errorCode":"H2Q5t",

"message":"PueCb",

"status":false

}
