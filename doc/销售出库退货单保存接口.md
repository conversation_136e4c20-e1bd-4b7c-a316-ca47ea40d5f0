## 销售出库退货单保存接口

### 接口描述：

- 广交云.【销售出库单】.修改操作.审核后 **推送** 金蝶云·星空旗舰版.【销售出库单】
- 而广交云.【销售出库单】.新增操作.需调用 **[6.1.下推并保存接口](#_下推并保存接口) 下推** 生成金蝶云·星空旗舰版.【销售出库单】，然后获取返回的目标单据id和目标单据分录id，再调用本接口进行保存操作

- 广交云.【销售退货单】.修改操作.审核后 **推送** 金蝶云·星空旗舰版.【销售退货单】
- 而广交云.【销售退货单】.新增操作.需调用 **[6.1.下推并保存接口](#_下推并保存接口) 下推** 生成金蝶云·星空旗舰版.【销售退货单】，然后获取返回的目标单据id和目标单据分录id，再调用本接口进行保存操作

销售出库单与销售退货单保存接口相同。

从销售出库单转为销售退货单需要改以下字段：

1、单据类型改为im_PurInBill_STD_BT_S_R

1.  业务类型编码和库存事务编码都改为退料相关如，2101

销售出库单 单据类型编码：im_SalOutBill_STD_BT_S

销售退货单 单据类型编码：im_SalOutBill_STD_BT_S_R

业务类型编码：210-物料类销售、2101-物料类销售退货、2102-物料类销售退补货、212-服务类销售、213-费用类销售、214-销售费用、220-直运销售、2201-直运销售退货、231-委托代销售出、2311-委托代销售出退回、240-VMI销售、2401-VMI销售退货

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/im_saloutbill/saveSaloutBill

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/im_saloutbill/saveSaloutBill](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| org_number | String | 是   | 库存组织.编码 | 1   | 固定值，传"1011" |
| billno | String | 否   | 单据编号 | 1   | "5Xrbr" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "XSDD-20250505-005"，传广交云单号 |
| &nbsp;      gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
| billtype_number | String | 是   | 单据类型.编码(<br><br>销售出库单-im_SalOutBill_STD_BT_S<br><br>销售退货单-im_SalOutBill_STD_BT_S_R<br><br>) | 1   | 销售出库单-im_SalOutBill_STD_BT_S<br><br>销售退货单-im_SalOutBill_STD_BT_S_R |
| biztype_number | String | 是   | 业务类型.编码(<br><br>210-物料类销售<br><br>2101-物料类销售退货<br><br>) | 1   | 如为销售出库单时传"210"，<br><br>如为销售退货单时传"2101" |
| invscheme_number | String | 是   | 库存事务.编码(<br><br>210-物料类销售<br><br>2101-普通销售退、补货<br><br>) | 1   | 如为销售出库单时传"210"，<br><br>如为销售退货单时传"2101" |
| biztime | Date | 是   | 业务日期 | 1   | "2022-07-04" |
| exratedate | Date | 是   | 汇率日期 | 1   | "2022-07-04"，同业务日期 |
| customer_number | String | 是   | 客户.编码 | 1   | "CUS001" |
| paymode | String | 是   | 付款方式（CREDIT-赊销、CASH-现销） | 1   | "CREDIT"，广交云零售的传现销，批发等传赊销 |
| bizorg_number | String | 是   | 销售组织.编码 | 1   | 固定值，传"1011" |
| currency_number | String | 是   | 本位币.货币代码 | 1   | 固定值，传"CNY"(人民币) |
| settlecurrency_number | String | 是   | 结算币别.货币代码 | 1   | 固定值，传"CNY"(人民币) |
| exratetable_number | String | 是   | 汇率表.编码 | 1   | 固定值，传"ERT-01" |
| exchangerate | Decimal | 是   | 汇率  | 1   | 固定值，传1 |
| istax | Boolean | 是   | 含税  | 1   | true |
| reccondition_number | String | 是   | 收款条件.编号(<br><br>对照 《收款条件.xlsx》) | 1   | 传对应的收款条件.编号 |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| id  | String | 否   | 物料明细.id | 2   | 修改时需传 |
| linetype_number | String | 是   | 行类型.编码(010-物资) | 2   | 固定值，传"010" |
| material_number | String | 是   | 物料编码.编码 | 2   | "item-001"，产品编码 |
| unit_number | String | 是   | 库存单位.编码 | 2   | "kg" |
| baseunit_number | String | 是   | 基本单位.编码 | 2   | "kg"，同库存单位.编码 |
| qty | Decimal | 是   | 物料明细.数量 | 2   | "1" |
| baseqty | Decimal | 是   | 物料明细.基本数量 | 2   | "645.39"，同物料明细.数量 |
| warehouse_number | String | 是   | 仓库.编码 | 2   | "CK001" |
| outinvtype_number | String | 是   | 出库库存类型.编码（110-普通、111-赠品、113-VMI） | 2   | 固定值，传"110" |
| outinvstatus_number | String | 是   | 出库库存状态.编码(110-可用) | 2   | 固定值，传"110" |
| outownertype | String | 是   | 物料明细.出库货主类型 bd_supplier:供应商, bd_customer:客户, bos_org:核算组织 | 2   | 固定值，传"bos_org" |
| outowner_number | String | 是   | 出库货主.编码 | 2   | 固定值，传"1011" |
| outkeepertype | String | 是   | 物料明细.出库保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| outkeeper_number | String | 是   | 出库保管者.编码 | 2   | 固定值，传"1011" |
| entrysettleorg_number | String | 是   | 结算组织.编码 | 2   | 固定值，传"1011" |
| price | Decimal | 是   | 物料明细.单价 | 2   | "419.59" |
| priceandtax | Decimal | 是   | 物料明细.含税单价 | 2   | "926.30" |
| &nbsp; taxrateid_number | String | 否   | 税率.编码 | 2   | "V5"，广交云做映射，传对应编码 |
| &nbsp; taxamount | Decimal | 否   | 物料明细.税额 | 2   | "0.9" |
| &nbsp; amountandtax | Decimal | 否   | 物料明细.价税合计 | 2   | "975.00" |
| &nbsp; amount | Decimal | 否   | 物料明细.金额 | 2   | "135.46" |
| &nbsp; ispresent | Boolean | 否   | 物料明细.赠品 | 2   | 默认为false，金额为0传true |
| discounttype | String | 是   | 物料明细.折扣方式 A:折扣率(%), NULL:无, B:单位折扣额 | 2   | "NULL" |
| &nbsp; discountrate | Decimal | 否   | 物料明细.单位折扣(率) | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; discountamount | Decimal | 否   | 物料明细.折扣额 | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; entrycomment | String | 否   | 物料明细.备注 | 2   | "备注文本" |
| &nbsp; project_number | String | 否   | 项目编码.项目编码 | 2   | "IHUsI" |
| &nbsp; producedate | Date | 否   | 物料明细.生产日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| &nbsp; expirydate | Date | 否   | 物料明细.到期日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| lotnumber | String | 否   | 物料明细.批号 | 2   | "IHUsI"，对应广交云批次，物料启用批号时必传 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data": \[

&nbsp;       {

&nbsp;           "customer_number": "Cus-000004",

&nbsp;           "iswholediscount": false,

&nbsp;           "billtype_number": "im_SalOutBill_STD_BT_S",

&nbsp;           "biztime": "2025-08-06",

&nbsp;           "exratedate": "2025-08-06",

&nbsp;           "wholediscountamount": "0",

&nbsp;           "org_number": "1011",

&nbsp;           "bizorg_number": "1011",

&nbsp;           "comment": "备注文本",

&nbsp;           "currency_number": "CNY",

&nbsp;           "settlecurrency_number": "CNY",

&nbsp;           "istax": true,

&nbsp;           "gjwl_thirdparty_billno": "XSCK-250729-000002",

&nbsp;           "gjwl_sourcesystemtype": "广交云供应链管理系统",

&nbsp;           "biztype_number": "210",

&nbsp;           "invscheme_number": "210",

&nbsp;           "exratetable_number": "ERT-01",

&nbsp;           "exchangerate": "1",

&nbsp;           "paymode": "CREDIT",

&nbsp;           "billentry": \[

&nbsp;               {

&nbsp;                   "e_stockorg_number": "1011",

&nbsp;                   "entrysettleorg_number": "1011",

&nbsp;                   "taxrateid_number": "V5",

&nbsp;                   "remark": "备注文本",

&nbsp;                   "ispresent": false,

&nbsp;                   "unit_number": "pcs",

&nbsp;                   "baseunit_number": "pcs",

&nbsp;                   "price": "9.52",

&nbsp;                   "amount": "19.05",

&nbsp;                   "taxamount": "0.95",

&nbsp;                   "linetype_number": "010",

&nbsp;                   "priceandtax": "10.00",

&nbsp;                   "discounttype": "B",

&nbsp;                   "discountrate": "2.00",                  

&nbsp;                   "discountamount": "4.00",

&nbsp;                   "warehouse_number": "CK-001",

&nbsp;                   "material_number": "Item-*********",

&nbsp;                   "qty": "2",

&nbsp;                   "baseqty": "2",

&nbsp;                   "outkeepertype": "bos_org",

&nbsp;                   "outkeeper_number": "1011",

&nbsp;                   "outownertype": "bos_org",

&nbsp;                   "outowner_number": "1011",

&nbsp;                   "outinvstatus_number": "110",

&nbsp;                   "outinvtype_number": "110",

&nbsp;                   "producedate": "2025-07-29",

&nbsp;                   "expirydate": "2029-07-29",

&nbsp;                   "lotnumber": "123",

&nbsp;                   "gjwl_product_lotno": "123"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": true,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2275583594141335552",

&nbsp;               "keys": {

&nbsp;                   "id": ""

&nbsp;               },

&nbsp;               "number": "XSCK-250806-000012",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": null,

&nbsp;   "status": true

}
