## 附件上传接口

### 接口描述：用于上传附件并绑定附件到对应单据的接口

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/frame/attachment/uploadFile

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/frame/attachment/uploadFile

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:multipart/form-data

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **字段参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| attachmentUploadFileArgs | AttachmentUploadFileArgs | 是   | 上传绑定附件信息 | 1   | \-  |
| entityNumber | String | 是   | 源实体标识(<br><br>如为其他出库单传:"im_otheroutbill";<br><br>如为盘亏单传:"<br><br>im_deficitbill";<br><br>如为付款申请单传:"<br><br>ap_payapply";<br><br>) | 2   | "sgkyD" |
| billPkId | Object | 是   | 单据主键，即单据Id | 2   | 1957409102693941248 |
| entryPkId | Object | 否   | 单据体行主键 | 2   | 1957409102693941249 |
| subEntryPkId | Object | 否   | 子单据体行主键 | 2   | 1957409102693941250 |
| extFormNumber | String | 否   | 附件控件在扩展表单上添加时需填写扩展表单标识 | 2   | "hycgA" |
| controlKey | String | 是   | 附件上传到控件(附件字段、附件面板)的标识 | 2   | "FaekA" |
| description | String | 否   | 附件备注 | 2   | "7RGKC" |
| appNumber | String | 否   | 应用编码 | 2   | "bt40k" |
| file | OpenApiFile | 是   | 文件内容 | 1   | \-  |

**请求参数示例（沙箱环境）（其他出库单-对应广交云.【报损单(不合格报损)】上传附件）:**

{

&nbsp;   "attachmentUploadFileArgs":{

&nbsp;       "entityNumber":"im_otheroutbill",

&nbsp;       "billPkId":"2290610299448910848",

&nbsp;       "entryPkId":null,

&nbsp;       "subEntryPkId":null,

&nbsp;       "extFormNumber":null,

&nbsp;       "controlKey":"attachmentpanel",

&nbsp;       "description":"测试上传其他出库单附件",

&nbsp;       "appNumber":null

&nbsp;   },

&nbsp;   "file":"-"

}

**postman示例文件（复制下面内容到json文件后导入postman）：**

{

"info": {

"\_postman_id": "9344d690-39bd-403b-9e67-251b91397058",

"name": "7.文件服务相关接口",

"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"

},

"item": \[

{

"name": "7.1.附件上传接口 Copy",

"request": {

"method": "POST",

"header": \[

{

"key": "x-acgw-identity",

"type": "text",

"value": "djF8MTkyOGIzZmYzZTIwMzgyYzIzMDF8NDg4MjUxMzA3Nzc5OXxFC68LZ4d-8Se3YvNKAhxG5E6W4P7M0yRbko5Eg4K-X3w="

},

{

"key": "accesstoken",

"type": "text",

"value": "OPENAPIAUTH_MjA1OTYyMDU5MTY4NTY5OTU4NF9DUXZPV1FSQkpEdUxXZnhKWjY0VEtrYWpyMXd0MW5nSnBWeEV3QnFQd21QOUI0WHZ4VDl5REthMnY0ejQzOGM4RWZ3bzdTM1FHeTQ4TjFTMWhJUlRqb1VDNVhkVzFPZ2I0TmE1MDI="

}

\],

"body": {

"mode": "formdata",

"formdata": \[

{

"key": "file",

"type": "file",

"src": "/C:/Users/<USER>/Pictures/Weixin.ico"

},

{

"key": "attachmentUploadFileArgs",

"value": "{\\n\\t\\t\\"entityNumber\\":\\"im_otheroutbill\\",\\n\\t\\t\\"billPkId\\":\\"2290610299448910848\\",\\n\\t\\t\\"controlKey\\":\\"attachmentpanel\\",\\n\\t\\t\\"description\\":\\"测试上传其他出库单附件\\",\\n\\t\\t\\"appNumber\\":null\\n\\t}",

"type": "text"

}

\],

"options": {

"raw": {

"language": "json"

}

}

},

"url": {

"raw": "https://gjwl.test.kdgalaxy.com/kapi/v2/frame/attachment/uploadFile",

"protocol": "https",

"host": \[

"gjwl",

"test",

"kdgalaxy",

"com"

\],

"path": \[

"kapi",

"v2",

"frame",

"attachment",

"uploadFile"

\]

}

},

"response": \[\]

}

\]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| status | 状态：true - 成功, false - 失败 | boolean | 1   | false |
| errorCode | 错误代码 | String | 1   | "QjDKv" |
| message | 错误信息 | String | 1   | "nHfcH" |
| data | data | AttachmentUploadFileResult | 1   | \-  |
| path | 文件路径 | String | 2   | "crhlw" |

**返回参数成功示例:**

{

&nbsp;   "data": {

&nbsp;       "path": "1fc9f1a3e3800c00"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": null,

&nbsp;   "status": true

}

**返回参数错误示例：**

{

&nbsp;   "data": null,

&nbsp;   "errorCode": "error",

&nbsp;   "message": "单据数据不存在。",

&nbsp;   "status": false

}