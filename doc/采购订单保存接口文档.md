## 采购订单保存接口

### 接口描述：

- 广交云.【采购订单】.新增/修改操作.审核后 **推送** 金蝶云·星空旗舰版.【采购订单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/pm/pm_purorderbill/savePurOrder

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/pm/pm_purorderbill/savePurOrder](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| billno | String | 否   | 单据编号 | 1   | "sBaLJ" |
| gjwl_thirdparty_billno | String | 否   | 外部系统单号 | 1   | "pOaDR" |
| gjwl_sourcesystemtype | String | 否   | 来源系统 | 1   | "广交云供应链管理系统" |
| org_number | String | 是   | 采购组织.编码 | 1   | 固定值，传"1011" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | "pm_PurOrderBill_STD_BT_S" |
| biztype_number | String | 是   | 业务类型.编码 | 1   | "110" |
| dept_number | String | 否   | 采购部门.编码(GJEY1102-采购中心) | 1   | 固定值，传"GJEY1102<br><br>" |
| biztime | Date | 是   | 订单日期 | 1   | "2023-10-23" |
| supplier_number | String | 是   | 订货供应商.编码 | 1   | "Sup-004003" |
| paycondition_number | String | 是   | 付款条件.编号(<br><br>对照 《付款条件.xlsx》) | 1   | 传对应的付款条件.编号 |
| settlecurrency_number | String | 是   | 结算币别.货币代码 | 1   | 固定值，传"CNY" |
| comment | String | 否   | 备注  | 1   | "9kZ8k" |
| istax | Boolean | 否   | 含税  | 1   | true |
| paymode | String | 是   | 付款方式 CASH:现购, CREDIT:赊购 | 1   | 固定值，传"CREDIT" |
| exratedate | Date | 是   | 汇率日期 | 1   | "2022-07-04"，同业务日期 |
| exratetable_number | String | 是   | 汇率表.编码 | 1   | 固定值，传"ERT-01" |
| exchangerate | Decimal | 是   | 汇率  | 1   | 固定值，传1 |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| id  | Long | 否   | 物料明细.id | 2   | "3283243790686923776"，修改时必传 |
| linetype_number | String | 是   | 行类型.编码<br><br>(010-物资) | 2   | 固定值，传"010" |
| material_number | String | 是   | 物料编码.编码 | 2   | "1.01.100-0066B-000" |
| unit_number | String | 是   | 采购单位.编码 | 2   | "pcs" |
| qty | Decimal | 是   | 物料明细.数量 | 2   | "743.86" |
| price | Decimal | 否   | 物料明细.单价 | 2   | "392.21" |
| priceandtax | Decimal | 否   | 物料明细.含税单价 | 2   | "118.31" |
| warehouse_number | String | 否   | 仓库.编码 | 2   | "en01" |
| discounttype | String | 是   | 物料明细.折扣方式 A:折扣率(%), B:单位折扣额, NULL:无 | 2   | "NULL" |
| &nbsp; discountrate | Decimal | 否   | 物料明细.单位折扣(率) | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; discountamount | Decimal | 否   | 物料明细.折扣额 | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| taxrateid_number | String | 否   | 税率.编码 | 2   | "V5"，广交云做映射，传对应编码 |
| entryreqorg_number | String | 是   | 需求组织.编码 | 2   | 固定值，传"1011" |
| entryrecorg_number | String | 是   | 收料组织.编码 | 2   | 固定值，传"1011" |
| entrysettleorg_number | String | 是   | 结算组织.编码 | 2   | 固定值，传"1011" |
| ownertype | String | 是   | 物料明细.货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| owner_number | String | 是   | 货主.编码 | 2   | 固定值，传"1011" |
| promisedate | Date | 否   | 物料明细.承诺日期 | 2   | "2023-10-23" |
| deliverdate | Date | 是   | 物料明细.交货日期 | 2   | "2023-10-23" |
| ispresent | Boolean | 否   | 物料明细.赠品 | 2   | false |
| project_number | String | 否   | 项目编码.项目编码 | 2   | "test项目002" |
| entrycomment | String | 否   | 物料明细.备注 | 2   | "fEARc" |
| ispayrate | Boolean | 否   | 按比例(%) | 1   | true |
| purbillentry_pay | Entries | 否   | 付款计划 | 1   | {},{} |
| id  | Long | 否   | 付款计划.id | 2   | "5640411716278210560" |
| planentrysettleorg_number | String | 否   | 结算组织.编码 | 2   | 固定值，传"1011" |
| paydate | Date | 否   | 付款计划.到期日 | 2   | "2024-08-29" |
| payrate | Decimal | 否   | 付款计划.应付比例(%) | 2   | "96.94" |
| payamount | Decimal | 否   | 付款计划.应付金额 | 2   | "234.46" |
| isprepay | Boolean | 否   | 付款计划.是否预付 | 2   | false |
| trdbillno | String | 是   | 第三方业务编码 | 1   | "mcj6C"，传外部系统单号 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data": \[

&nbsp;       {

&nbsp;           "billno": "CGDD-250911-000004",

&nbsp;           "org_number": "1011",

&nbsp;           "billtype_number": "pm_PurOrderBill_STD_BT_S",

&nbsp;           "biztype_number": "110",

&nbsp;           "dept_number": "GJEY1102",

&nbsp;           "biztime": "2025-09-11",

&nbsp;           "exratedate": "2025-09-11",

&nbsp;           "supplier_number": "1111003",

&nbsp;           "paycondition_number": "FKTJ-YF-100",

&nbsp;           "settlecurrency_number": "CNY",

&nbsp;           "comment": "备注",

&nbsp;           "istax": true,

&nbsp;           "paymode": "CREDIT",

&nbsp;           "exratetable_number": "ERT-01",

&nbsp;           "exchangerate": "1",

&nbsp;           "ispayrate": false,

&nbsp;           "gjwl_thirdparty_billno": "CGDD-250911-000001",

&nbsp;           "gjwl_sourcesystemtype": "广交云供应链管理系统",

&nbsp;           "billentry": \[

&nbsp;               {

&nbsp;                   "linetype_number": "010",

&nbsp;                   "material_number": "Item-*********",

&nbsp;                   "unit_number": "pcs",

&nbsp;                   "qty": "100",

&nbsp;                   "price": "9.52",

&nbsp;                   "priceandtax": "10",

&nbsp;                   "warehouse_number": "CK-001",

&nbsp;                   "discounttype": "B",

&nbsp;                   "taxrateid_number": "V5",

&nbsp;                   "entryreqorg_number": "1011",

&nbsp;                   "entryrecorg_number": "1011",

&nbsp;                   "entrysettleorg_number": "1011",

&nbsp;                   "ownertype": "bos_org",

&nbsp;                   "owner_number": "1011",

&nbsp;                   "promisedate": "2025-09-11",

&nbsp;                   "deliverdate": "2025-09-11",

&nbsp;                   "ispresent": false,

&nbsp;                   "project_number": "KD-P-ALL_SYS",

&nbsp;                   "entrycomment": "单据体备注",

&nbsp;                   "discountrate": "0.10",

&nbsp;                   "taxamount": "47.14",

&nbsp;                   "discountamount": "10.00",

&nbsp;                   "amountandtax": "990.00",

&nbsp;                   "amount": "942.86"

&nbsp;               }

&nbsp;           \],

&nbsp;           "purbillentry_pay":\[

&nbsp;               {

&nbsp;                   "planentrysettleorg_number":"1011",

&nbsp;                   "payrate":"100.00",

&nbsp;                   "payamount":"990",

&nbsp;                   "isprepay":true

&nbsp;               }

&nbsp;           \],

&nbsp;           "trdbillno":"CGDD-250911-000001"

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| id  | "2274145548736921600" | String | 2   | 返回单据Id |
| number | "CGRK-250804-000001" | String | 2   | 返回单据编号 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": true,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2290027758555811840",

&nbsp;               "keys": {

&nbsp;                   "billno": "CGDD-250826-000002"

&nbsp;               },

&nbsp;               "number": "CGDD-250826-000002",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": null,

&nbsp;   "status": true

}
