package com.polarbear.kd.enums;

import lombok.Getter;

@Getter
public enum KingdeeBillTypeEnum {
  SURPLUS_BILL("im_surplusbill", "盘盈单"),
  DEFICIT_BILL("im_deficitbill", "盘亏单"),
  OTHER_OUT_BILL("im_otheroutbill", "其他出库单"),
  PAY_APPLY("ap_payapply", "付款申请单"),
  ;

  private final String type;
  private final String name;

  KingdeeBillTypeEnum(String type, String name) {
    this.type = type;
    this.name = name;
  }
}
