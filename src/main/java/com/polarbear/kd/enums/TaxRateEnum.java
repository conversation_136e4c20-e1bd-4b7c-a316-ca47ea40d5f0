package com.polarbear.kd.enums;

import java.math.BigDecimal;
import lombok.Getter;

@Getter
public enum TaxRateEnum {
  ZZS_17("zzs", new BigDecimal("0.17"), "001001", "增值税17%"),
  ZZS_16("zzs", new BigDecimal("0.16"), "001002", "增值税16%"),
  ZZS_11("zzs", new BigDecimal("0.11"), "001004", "增值税11%"),
  ZZS_10("zzs", new BigDecimal("0.10"), "001005", "增值税10%"),
  ZZS_2("zzs", new BigDecimal("0.2"), "001009", "增值税2%"),
  ZZS_1("zzs", new BigDecimal("0.1"), "001012", "增值税1%"),
  ZZS_FREE("zzs", BigDecimal.ZERO, "001013", "增值税免税"),
  QYSDS_25("qysds", new BigDecimal("0.25"), "002001", "所得税25%"),
  QYSDS_20("qysds", new BigDecimal("0.20"), "002002", "所得税20%"),
  QYSDS_15("qysds", new BigDecimal("0.15"), "002003", "所得税15%"),
  QYSDS_10("qysds", new BigDecimal("0.10"), "002004", "所得税10%"),
  ;

  private final String type;
  private final BigDecimal rate;
  private final String code;
  private final String description;

  TaxRateEnum(String type, BigDecimal rate, String code, String description) {
    this.type = type;
    this.rate = rate;
    this.code = code;
    this.description = description;
  }

  public static String getZZSCodeByRate(BigDecimal rate) {
    for (TaxRateEnum taxRateEnum : TaxRateEnum.values()) {
      if (taxRateEnum.getType().equals("zzs") && taxRateEnum.getRate().compareTo(rate) == 0) {
        return taxRateEnum.getCode();
      }
    }

    return null;
  }
}
