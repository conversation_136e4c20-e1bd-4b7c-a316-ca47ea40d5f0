package com.polarbear.kd.enums;

import lombok.Getter;

/**
 * 收款条件枚举类
 */
@Getter
public enum RecConditionEnum {
  SKTJ_DJ_FH_ZQ_05(1, 2, 1, 5, "SKTJ-DJ-FH-ZQ-05", "单结-发货时间-账期5天"),
  SKTJ_DJ_FH_ZQ_07(1, 2, 1, 7, "SKTJ-DJ-FH-ZQ-07", "单结-发货时间-账期7天"),
  SKTJ_DJ_FH_ZQ_08(1, 2, 1, 8, "SKTJ-DJ-FH-ZQ-08", "单结-发货时间-账期8天"),
  SKTJ_DJ_FH_ZQ_15(1, 2, 1, 15, "SKTJ-DJ-FH-ZQ-15", "单结-发货时间-账期15天"),
  SKTJ_DJ_FH_ZQ_18(1, 2, 1, 18, "SKTJ-DJ-FH-ZQ-18", "单结-发货时间-账期18天"),
  SKTJ_DJ_FH_ZQ_30(1, 2, 1, 30, "SKTJ-DJ-FH-ZQ-30", "单结-发货时间-账期30天"),
  SKTJ_DJ_FH_ZQ_45(1, 2, 1, 45, "SKTJ-DJ-FH-ZQ-45", "单结-发货时间-账期45天"),
  SKTJ_DJ_FH_ZQ_60(1, 2, 1, 60, "SKTJ-DJ-FH-ZQ-60", "单结-发货时间-账期60天"),
  SKTJ_DJ_FH_ZQ_90(1, 2, 1, 90, "SKTJ-DJ-FH-ZQ-90", "单结-发货时间-账期90天"),
  SKTJ_DJ_SH_ZQ_05(1, 2, 2, 5, "SKTJ-DJ-SH-ZQ-05", "单结-收货时间-账期5天"),
  SKTJ_DJ_SH_ZQ_07(1, 2, 2, 7, "SKTJ-DJ-SH-ZQ-07", "单结-收货时间-账期7天"),
  SKTJ_DJ_SH_ZQ_08(1, 2, 2, 8, "SKTJ-DJ-SH-ZQ-08", "单结-收货时间-账期8天"),
  SKTJ_DJ_SH_ZQ_15(1, 2, 2, 15, "SKTJ-DJ-SH-ZQ-15", "单结-收货时间-账期15天"),
  SKTJ_DJ_SH_ZQ_18(1, 2, 2, 18, "SKTJ-DJ-SH-ZQ-18", "单结-收货时间-账期18天"),
  SKTJ_DJ_SH_ZQ_30(1, 2, 2, 30, "SKTJ-DJ-SH-ZQ-30", "单结-收货时间-账期30天"),
  SKTJ_DJ_SH_ZQ_45(1, 2, 2, 45, "SKTJ-DJ-SH-ZQ-45", "单结-收货时间-账期45天"),
  SKTJ_DJ_SH_ZQ_60(1, 2, 2, 60, "SKTJ-DJ-SH-ZQ-60", "单结-收货时间-账期60天"),
  SKTJ_DJ_SH_ZQ_90(1, 2, 2, 90, "SKTJ-DJ-SH-ZQ-90", "单结-收货时间-账期90天"),
  SKTJ_YJ_FH_ZQ_07(1, 1, 1, 7, "SKTJ-YJ-FH-ZQ-07", "月结-发货时间-账期7天"),
  SKTJ_YJ_FH_ZQ_30(1, 1, 1, 30, "SKTJ-YJ-FH-ZQ-30", "月结-发货时间-账期30天"),
  SKTJ_YJ_SH_ZQ_07(1, 1, 2, 7, "SKTJ-YJ-SH-ZQ-07", "月结-收货时间-账期7天"),
  SKTJ_YJ_SH_ZQ_15(1, 1, 2, 15, "SKTJ-YJ-SH-ZQ-15", "月结-收货时间-账期15天"),
  SKTJ_YJ_SH_ZQ_25(1, 1, 2, 25, "SKTJ-YJ-SH-ZQ-25", "月结-收货时间-账期25天"),
  SKTJ_YJ_SH_ZQ_30(1, 1, 2, 30, "SKTJ-YJ-SH-ZQ-30", "月结-收货时间-账期30天"),
  SKTJ_YJ_SH_ZQ_45(1, 1, 2, 45, "SKTJ-YJ-SH-ZQ-45", "月结-收货时间-账期45天"),
  SKTJ_YJ_SH_ZQ_50(1, 1, 2, 50, "SKTJ-YJ-SH-ZQ-50", "月结-收货时间-账期50天"),
  SKTJ_YJ_SH_ZQ_60(1, 1, 2, 60, "SKTJ-YJ-SH-ZQ-60", "月结-收货时间-账期60天"),
  SKTJ_YJ_SH_ZQ_90(1, 1, 2, 90, "SKTJ-YJ-SH-ZQ-90", "月结-收货时间-账期90天"),
  SKTJ_YS_100(2, null, null, null, "SKTJ-YS-100", "预收100%"),
  ;

  private final Integer settleWay;
  private final Integer paymentWay;
  private final Integer paymentTimeType;
  private final Integer paymentDay;
  private final String code;
  private final String description;

  /**
   * 构造函数
   *
   * @param code        收款条件编号
   * @param description 收款条件描述
   */
  RecConditionEnum(
      Integer settleWay, Integer paymentWay, Integer paymentTimeType,
      Integer paymentDay, String code, String description) {
    this.settleWay = settleWay;
    this.paymentWay = paymentWay;
    this.paymentTimeType = paymentTimeType;
    this.paymentDay = paymentDay;
    this.code = code;
    this.description = description;
  }

  public static RecConditionEnum fromCondition(
      Integer settleWay, Integer paymentWay, Integer paymentTimeType, Integer paymentDay) {
    if (settleWay == 2) {
      return RecConditionEnum.SKTJ_YS_100;
    } else {
      for (RecConditionEnum condition : RecConditionEnum.values()) {
        if (condition.getSettleWay().equals(settleWay) && condition.getPaymentWay()
            .equals(paymentWay) && condition.getPaymentTimeType().equals(paymentTimeType)
            && condition.getPaymentDay().equals(paymentDay)) {
          return condition;
        }
      }
    }
    return null;
  }

  public static RecConditionEnum fromCode(String code) {
    for (RecConditionEnum condition : RecConditionEnum.values()) {
      if (condition.getCode().equals(code)) {
        return condition;
      }
    }
    return null;
  }

  public static RecConditionEnum fromDescription(String description) {
    for (RecConditionEnum condition : RecConditionEnum.values()) {
      if (condition.getDescription().equals(description)) {
        return condition;
      }
    }
    return null;
  }

  @Override
  public String toString() {
    return "RecConditionEnum{"
        + "code='"
        + code
        + '\''
        + ", description='"
        + description
        + '\''
        + '}';
  }
}
