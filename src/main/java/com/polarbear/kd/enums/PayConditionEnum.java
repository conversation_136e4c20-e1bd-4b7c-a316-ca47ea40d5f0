package com.polarbear.kd.enums;

import lombok.Getter;

/** 收款条件枚举类 */
@Getter
public enum PayConditionEnum {
  FKTJ_DJ_SH_ZQ_00(2, 1, 2, 0, "FKTJ-DJ-SH-ZQ-00", "单结，账期0天，收货后付款"),
  FKTJ_DJ_SH_ZQ_01(2, 1, 2, 1, "FKTJ-DJ-SH-ZQ-01", "单结-收货时间-账期1天"),
  FKTJ_DJ_SH_ZQ_03(2, 1, 2, 3, "FKTJ-DJ-SH-ZQ-03", "单结-收货时间-账期3天"),
  FKTJ_DJ_SH_ZQ_05(2, 1, 2, 5, "FKTJ-DJ-SH-ZQ-05", "单结-收货时间-账期5天"),
  FKTJ_DJ_SH_ZQ_07(2, 1, 2, 7, "FKTJ-DJ-SH-ZQ-07", "单结-收货时间-账期7天"),
  FKTJ_DJ_SH_ZQ_10(2, 1, 2, 10, "FKTJ-DJ-SH-ZQ-10", "单结-收货时间-账期10天"),
  FKTJ_DJ_SH_ZQ_120(2, 1, 2, 120, "FKTJ-DJ-SH-ZQ-120", "单结-收货时间-账期120天"),
  FKTJ_DJ_SH_ZQ_15(2, 1, 2, 15, "FKTJ-DJ-SH-ZQ-15", "单结-收货时间-账期15天"),
  FKTJ_DJ_SH_ZQ_150(2, 1, 2, 150, "FKTJ-DJ-SH-ZQ-150", "单结-收货时间-账期150天"),
  FKTJ_DJ_SH_ZQ_180(2, 1, 2, 180, "FKTJ-DJ-SH-ZQ-180", "单结-收货时间-账期180天"),
  FKTJ_DJ_SH_ZQ_210(2, 1, 2, 210, "FKTJ-DJ-SH-ZQ-210", "单结-收货时间-账期210天"),
  FKTJ_DJ_SH_ZQ_25(2, 1, 2, 25, "FKTJ-DJ-SH-ZQ-25", "单结-收货时间-账期25天"),
  FKTJ_DJ_SH_ZQ_30(2, 1, 2, 30, "FKTJ-DJ-SH-ZQ-30", "单结-收货时间-账期30天"),
  FKTJ_DJ_SH_ZQ_40(2, 1, 2, 40, "FKTJ-DJ-SH-ZQ-40", "单结-收货时间-账期40天"),
  FKTJ_DJ_SH_ZQ_45(2, 1, 2, 45, "FKTJ-DJ-SH-ZQ-45", "单结-收货时间-账期45天"),
  FKTJ_DJ_SH_ZQ_60(2, 1, 2, 60, "FKTJ-DJ-SH-ZQ-60", "单结-收货时间-账期60天"),
  FKTJ_DJ_SH_ZQ_65(2, 1, 2, 65, "FKTJ-DJ-SH-ZQ-65", "单结-收货时间-账期65天"),
  FKTJ_DJ_SH_ZQ_90(2, 1, 2, 90, "FKTJ-DJ-SH-ZQ-90", "单结-收货时间-账期90天"),
  FKTJ_YJ_SH_ZQ_01(2, 2, 2, 1, "FKTJ-YJ-SH-ZQ-01", "月结-收货时间-账期1天"),
  FKTJ_YJ_SH_ZQ_03(2, 2, 2, 3, "FKTJ-YJ-SH-ZQ-03", "月结-收货时间-账期3天"),
  FKTJ_YJ_SH_ZQ_05(2, 2, 2, 5, "FKTJ-YJ-SH-ZQ-05", "月结-收货时间-账期5天"),
  FKTJ_YJ_SH_ZQ_07(2, 2, 2, 7, "FKTJ-YJ-SH-ZQ-07", "月结-收货时间-账期7天"),
  FKTJ_YJ_SH_ZQ_10(2, 2, 2, 10, "FKTJ-YJ-SH-ZQ-10", "月结-收货时间-账期10天"),
  FKTJ_YJ_SH_ZQ_15(2, 2, 2, 15, "FKTJ-YJ-SH-ZQ-15", "月结-收货时间-账期15天"),
  FKTJ_YJ_SH_ZQ_25(2, 2, 2, 25, "FKTJ-YJ-SH-ZQ-25", "月结-收货时间-账期25天"),
  FKTJ_YJ_SH_ZQ_30(2, 2, 2, 30, "FKTJ-YJ-SH-ZQ-30", "月结-收货时间-账期30天"),
  FKTJ_YJ_SH_ZQ_60(2, 2, 2, 60, "FKTJ-YJ-SH-ZQ-60", "月结-收货时间-账期60天"),
  FKTJ_YJ_SH_ZQ_90(2, 2, 2, 90, "FKTJ-YJ-SH-ZQ-90", "月结-收货时间-账期90天"),
  FKTJ_YF_JDZ(1, 2, null, null, "FKTJ-YF-JDZ", "预付按绝对值"),
  FKTJ_YF_100(1, 1, null, null, "FKTJ-YF-100", "预付100%"),
  ;

  private final Integer settleWay;
  private final Integer paymentWay;
  private final Integer paymentTimeType;
  private final Integer paymentDay;
  private final String code;
  private final String description;

  /**
   * 构造函数
   *
   * @param code 收款条件编号
   * @param description 收款条件描述
   */
  PayConditionEnum(
      Integer settleWay,
      Integer paymentWay,
      Integer paymentTimeType,
      Integer paymentDay,
      String code,
      String description) {
    this.settleWay = settleWay;
    this.paymentWay = paymentWay;
    this.paymentTimeType = paymentTimeType;
    this.paymentDay = paymentDay;
    this.code = code;
    this.description = description;
  }

  public static PayConditionEnum fromYFCondition(Integer settleWay, Integer prepayWay) {
    for (PayConditionEnum condition : PayConditionEnum.values()) {
      if (condition.getSettleWay().equals(settleWay)
          && condition.getPaymentWay().equals(prepayWay)) {
        return condition;
      }
    }
    return null;
  }

  public static PayConditionEnum fromCondition(
      Integer settleWay, Integer paymentWay, Integer paymentTimeType, Integer paymentDay) {
    for (PayConditionEnum condition : PayConditionEnum.values()) {
      if (condition.getSettleWay().equals(settleWay)
          && condition.getPaymentWay().equals(paymentWay)
          && condition.getPaymentTimeType().equals(paymentTimeType)
          && condition.getPaymentDay().equals(paymentDay)) {
        return condition;
      }
    }
    return null;
  }

  public static PayConditionEnum fromCode(String code) {
    for (PayConditionEnum condition : PayConditionEnum.values()) {
      if (condition.getCode().equals(code)) {
        return condition;
      }
    }
    return null;
  }

  public static PayConditionEnum fromDescription(String description) {
    for (PayConditionEnum condition : PayConditionEnum.values()) {
      if (condition.getDescription().equals(description)) {
        return condition;
      }
    }
    return null;
  }

  @Override
  public String toString() {
    return "PayConditionEnum{"
        + "code='"
        + code
        + '\''
        + ", description='"
        + description
        + '\''
        + '}';
  }
}
