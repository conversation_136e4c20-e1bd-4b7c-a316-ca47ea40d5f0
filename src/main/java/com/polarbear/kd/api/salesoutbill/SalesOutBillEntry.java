package com.polarbear.kd.api.salesoutbill;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售出库退货单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Data
public class SalesOutBillEntry {
    
    /**
     * 物料明细.id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 行类型.编码(010-物资) (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料编码.编码 (产品编码)
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 库存单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 基本单位.编码 (同库存单位.编码)
     */
    @JsonProperty("baseunit_number")
    private String baseunitNumber;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 物料明细.基本数量 (同物料明细.数量)
     */
    @JsonProperty("baseqty")
    private BigDecimal baseqty;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 出库库存类型.编码（110-普通、111-赠品、113-VMI） (固定值，传"110")
     */
    @JsonProperty("outinvtype_number")
    private String outinvtypeNumber;
    
    /**
     * 出库库存状态.编码(110-可用) (固定值，传"110")
     */
    @JsonProperty("outinvstatus_number")
    private String outinvstatusNumber;
    
    /**
     * 物料明细.出库货主类型 bd_supplier:供应商, bd_customer:客户, bos_org:核算组织 (固定值，传"bos_org")
     */
    @JsonProperty("outownertype")
    private String outownertype;
    
    /**
     * 出库货主.编码 (固定值，传"1011")
     */
    @JsonProperty("outowner_number")
    private String outownerNumber;
    
    /**
     * 物料明细.出库保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 (固定值，传"bos_org")
     */
    @JsonProperty("outkeepertype")
    private String outkeepertype;
    
    /**
     * 出库保管者.编码 (固定值，传"1011")
     */
    @JsonProperty("outkeeper_number")
    private String outkeeperNumber;
    
    /**
     * 结算组织.编码 (固定值，传"1011")
     */
    @JsonProperty("entrysettleorg_number")
    private String entrysettleorgNumber;
    
    /**
     * 物料明细.单价
     */
    @JsonProperty("price")
    private BigDecimal price;
    
    /**
     * 物料明细.含税单价
     */
    @JsonProperty("priceandtax")
    private BigDecimal priceandtax;
    
    /**
     * 税率.编码 (广交云做映射，传对应编码)
     */
    @JsonProperty("taxrateid_number")
    private String taxrateidNumber;
    
    /**
     * 物料明细.税额
     */
    @JsonProperty("taxamount")
    private BigDecimal taxamount;
    
    /**
     * 物料明细.价税合计
     */
    @JsonProperty("amountandtax")
    private BigDecimal amountandtax;
    
    /**
     * 物料明细.金额
     */
    @JsonProperty("amount")
    private BigDecimal amount;
    
    /**
     * 物料明细.赠品 (默认为false，金额为0传true)
     */
    @JsonProperty("ispresent")
    private Boolean ispresent;
    
    /**
     * 物料明细.折扣方式 A:折扣率(%), NULL:无, B:单位折扣额
     */
    @JsonProperty("discounttype")
    private String discounttype;
    
    /**
     * 物料明细.单位折扣(率) (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountrate")
    private BigDecimal discountrate;
    
    /**
     * 物料明细.折扣额 (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountamount")
    private BigDecimal discountamount;
    
    /**
     * 物料明细.备注
     */
    @JsonProperty("entrycomment")
    private String entrycomment;
    
    /**
     * 项目编码.项目编码
     */
    @JsonProperty("project_number")
    private String projectNumber;
    
    /**
     * 物料明细.生产日期 (物料启用保质期时必传)
     */
    @JsonProperty("producedate")
    private String producedate;
    
    /**
     * 物料明细.到期日期 (物料启用保质期时必传)
     */
    @JsonProperty("expirydate")
    private String expirydate;
    
    /**
     * 物料明细.批号 (对应广交云批次，物料启用批号时必传)
     */
    @JsonProperty("lotnumber")
    private String lotnumber;
    
    /**
     * 物料明细.生产批号 (对应广交云生产批号)
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
    
    // 行类型常量
    public static final String LINETYPE_MATERIAL = "010";
    
    // 出库库存类型常量
    public static final String OUTINVTYPE_NORMAL = "110";
    public static final String OUTINVTYPE_GIFT = "111";
    public static final String OUTINVTYPE_VMI = "113";
    
    // 出库库存状态常量
    public static final String OUTINVSTATUS_AVAILABLE = "110";
    
    // 货主类型常量
    public static final String OWNERTYPE_ORG = "bos_org";
    public static final String OWNERTYPE_SUPPLIER = "bd_supplier";
    public static final String OWNERTYPE_CUSTOMER = "bd_customer";
    
    // 折扣方式常量
    public static final String DISCOUNTTYPE_RATE = "A";
    public static final String DISCOUNTTYPE_AMOUNT = "B";
    public static final String DISCOUNTTYPE_NONE = "NULL";
}
