package com.polarbear.kd.api.salesoutbill;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售出库退货单实体类
 * 
 * <AUTHOR>
 * @apiNote 广交云.【销售出库单】.修改操作.审核后 推送 金蝶云·星空旗舰版.【销售出库单】
 *          广交云.【销售退货单】.修改操作.审核后 推送 金蝶云·星空旗舰版.【销售退货单】
 *          销售出库单与销售退货单保存接口相同，通过单据类型编码区分
 */
@Data
public class SalesOutBill {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 库存组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;
    
    /**
     * 外部系统单号 (传广交云单号)
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;
    
    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
    
    /**
     * 单据类型.编码
     * 销售出库单-im_SalOutBill_STD_BT_S
     * 销售退货单-im_SalOutBill_STD_BT_S_R
     */
    @JsonProperty("billtype_number")
    private String billtypeNumber;
    
    /**
     * 业务类型.编码
     * 210-物料类销售
     * 2101-物料类销售退货
     */
    @JsonProperty("biztype_number")
    private String biztypeNumber;
    
    /**
     * 库存事务.编码
     * 210-物料类销售
     * 2101-普通销售退、补货
     */
    @JsonProperty("invscheme_number")
    private String invschemeNumber;
    
    /**
     * 业务日期
     */
    @JsonProperty("biztime")
    private String biztime;
    
    /**
     * 汇率日期 (同业务日期)
     */
    @JsonProperty("exratedate")
    private String exratedate;
    
    /**
     * 客户.编码
     */
    @JsonProperty("customer_number")
    private String customerNumber;
    
    /**
     * 付款方式（CREDIT-赊销、CASH-现销）
     * 广交云零售的传现销，批发等传赊销
     */
    @JsonProperty("paymode")
    private String paymode;
    
    /**
     * 销售组织.编码 (固定值，传"1011")
     */
    @JsonProperty("bizorg_number")
    private String bizorgNumber;
    
    /**
     * 本位币.货币代码 (固定值，传"CNY"(人民币))
     */
    @JsonProperty("currency_number")
    private String currencyNumber;
    
    /**
     * 结算币别.货币代码 (固定值，传"CNY"(人民币))
     */
    @JsonProperty("settlecurrency_number")
    private String settlecurrencyNumber;
    
    /**
     * 汇率表.编码 (固定值，传"ERT-01")
     */
    @JsonProperty("exratetable_number")
    private String exratetableNumber;
    
    /**
     * 汇率 (固定值，传1)
     */
    @JsonProperty("exchangerate")
    private BigDecimal exchangerate;
    
    /**
     * 含税
     */
    @JsonProperty("istax")
    private Boolean istax;
    
    /**
     * 收款条件.编号 (对照《收款条件.xlsx》)
     */
    @JsonProperty("reccondition_number")
    private String recconditionNumber;
    
    /**
     * 录入整单折扣
     */
    @JsonProperty("iswholediscount")
    private Boolean iswholediscount;
    
    /**
     * 整单折扣额
     */
    @JsonProperty("wholediscountamount")
    private String wholediscountamount;
    
    /**
     * 备注
     */
    @JsonProperty("comment")
    private String comment;
    
    /**
     * 物料明细
     */
    @JsonProperty("billentry")
    private List<SalesOutBillEntry> billentry;
    
    // 单据类型常量
    public static final String BILLTYPE_SALES_OUT = "im_SalOutBill_STD_BT_S";
    public static final String BILLTYPE_SALES_RETURN = "im_SalOutBill_STD_BT_S_R";
    
    // 业务类型常量
    public static final String BIZTYPE_SALES = "210";
    public static final String BIZTYPE_SALES_RETURN = "2101";
    
    // 付款方式常量
    public static final String PAYMODE_CREDIT = "CREDIT";
    public static final String PAYMODE_CASH = "CASH";
}
