package com.polarbear.kd.api.salesoutbill;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 销售出库退货单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【销售出库单】.修改操作.审核后 推送 金蝶云·星空旗舰版.【销售出库单】
 *          广交云.【销售退货单】.修改操作.审核后 推送 金蝶云·星空旗舰版.【销售退货单】
 *          销售出库单与销售退货单保存接口相同，通过单据类型编码区分
 */
public class SalesOutBillSaveRequest extends KdOpRequest<List<SalesOutBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/im_saloutbill/saveSaloutBill";
    }

    @Override
    public String logModule() {
        return "kd.im_saloutbill.saveSaloutBill";
    }

    @Override
    public Class<SalesOutBillSaveResponse> getResponseClass() {
        return SalesOutBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<SalesOutBill>> setLogKey(Config<List<SalesOutBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(SalesOutBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
