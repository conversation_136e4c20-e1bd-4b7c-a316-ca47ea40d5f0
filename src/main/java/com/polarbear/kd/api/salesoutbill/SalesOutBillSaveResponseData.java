package com.polarbear.kd.api.salesoutbill;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 销售出库退货单保存响应数据
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Data
public class SalesOutBillSaveResponseData {
    
    /**
     * 返回结果详细信息
     */
    @JsonProperty("result")
    private List<SalesOutBillSaveResult> result;
    
    /**
     * 操作失败数量
     */
    @JsonProperty("failCount")
    private String failCount;
    
    /**
     * 操作成功数量
     */
    @JsonProperty("successCount")
    private String successCount;
    
    /**
     * 销售出库退货单保存结果项
     */
    @Data
    public static class SalesOutBillSaveResult {
        
        /**
         * 单据索引
         */
        @JsonProperty("billIndex")
        private Integer billIndex;
        
        /**
         * 单据状态
         */
        @JsonProperty("billStatus")
        private Boolean billStatus;
        
        /**
         * 错误信息数组
         */
        @JsonProperty("errors")
        private List<Object> errors;
        
        /**
         * 单据Id，需记录，后续修改和下推接口需用
         */
        @JsonProperty("id")
        private String id;
        
        /**
         * 主键信息
         */
        @JsonProperty("keys")
        private Keys keys;
        
        /**
         * 单据编号
         */
        @JsonProperty("number")
        private String number;
        
        /**
         * 操作类型
         */
        @JsonProperty("type")
        private String type;
    }
    
    /**
     * 主键信息
     */
    @Data
    public static class Keys {
        
        /**
         * 单据ID
         */
        @JsonProperty("id")
        private String id;
    }
}
