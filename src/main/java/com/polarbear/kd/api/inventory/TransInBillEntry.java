package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分步调入单明细实体类
 *
 * <AUTHOR>
 */
@Data
public class TransInBillEntry {

    /**
     * 物料明细.id
     */
    @JsonProperty("id")
    private String id;

    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;

    /**
     * 物料编码.编码
     */
    @JsonProperty("material_number")
    private String materialNumber;

    /**
     * 物料版本.物料版本编码
     */
    @JsonProperty("mversion_number")
    private String mversionNumber;

    /**
     * 库存单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;

    /**
     * 物料明细.数量
     */
    @JsonProperty("qty")
    private BigDecimal qty;

    /**
     * 调入仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;

    /**
     * 调入库存状态.编码(110: 可用) (固定值，传"110")
     */
    @JsonProperty("invstatus_number")
    private String invstatusNumber;

    /**
     * 物料明细.调入货主类型 (固定值，传"bos_org")
     * bos_org:业务组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("ownertype")
    private String ownertype;

    /**
     * 调入货主.编码 (固定值，传"BU-001")
     */
    @JsonProperty("owner_number")
    private String ownerNumber;

    /**
     * 物料明细.调入保管者类型 (固定值，传"bos_org")
     * bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("keepertype")
    private String keepertype;

    /**
     * 调入保管者.编码 (固定值，传"BU-001")
     */
    @JsonProperty("keeper_number")
    private String keeperNumber;

    /**
    * 物料明细.批号
    */
    @JsonProperty("lotnumber")
    private String lotnumber;

    /**
    * 物料明细.生产批号
    */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;

    /**
     * 物料明细.生产日期 (物料启用保质期时必传)
     */
    @JsonProperty("producedate")
    private String producedate;

    /**
     * 物料明细.有效期至 (物料启用保质期时必传)
     */
    @JsonProperty("expirydate")
    private String expirydate;

    /**
     * 调入库存类型.编码(110： 普通) (固定值，传"110")
     */
    @JsonProperty("invtype_number")
    private String invtypeNumber;

    /**
     * 调出库存状态.编码(114: 在途) (固定值，传"114")
     */
    @JsonProperty("outinvstatus_number")
    private String outinvstatusNumber;

    /**
     * 调出库存类型.编码(110： 普通) (固定值，传"110")
     */
    @JsonProperty("outinvtype_number")
    private String outinvtypeNumber;

    /**
     * 物料明细.调出货主类型 (固定值，传"bos_org")
     * bos_org:业务组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("outownertype")
    private String outownertype;

    /**
     * 物料明细.调出保管者类型 (固定值，传"bos_org")
     * bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("outkeepertype")
    private String outkeepertype;

    /**
     * 调出货主.编码
     */
    @JsonProperty("outowner_number")
    private String outownerNumber;

    /**
     * 调出保管者.编码
     */
    @JsonProperty("outkeeper_number")
    private String outkeeperNumber;

    /**
     * 调出仓库.编码
     */
    @JsonProperty("outwarehouse_number")
    private String outwarehouseNumber;

    /**
    * 在途货主类型 [bos_org:业务组织, bd_supplier:供应商, bd_customer:客户]  固定值，传"bos_org"
    */
    @JsonProperty("transitownertype")
    private String transitownertype;

    /**
    * 在途货主.编码  固定值，传"1011"
    */
    @JsonProperty("transitowner_number")
    private String transitowner_number;
}
