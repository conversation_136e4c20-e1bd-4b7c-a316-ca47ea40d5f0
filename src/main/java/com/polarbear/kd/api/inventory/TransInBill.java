package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 分步调入单实体类
 *
 * <AUTHOR>
 * @apiNote 广交云.【调拨单(调入)】.修改操作.审核后 推送 金蝶云·星空旗舰版.【分步调入单】
 */
@Data
public class TransInBill {

    /**
     * 单据id，调拨出库单下推接口获取
     */
    @JsonProperty("id")
    private String id;

    /**
     * 调入组织.编码 (固定值，传"BU-001")
     */
    @JsonProperty("org_number")
    private String orgNumber;

    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;

    /**
     * 外部系统单号 (传广交云单号)
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;

    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;

    /**
     * 业务日期
     */
    @JsonProperty("biztime")
    private String biztime;

    /**
     * 单据类型.编码 (固定值，传"im_AllotInBill_STD_BT_S")
     */
    @JsonProperty("billtype_number")
    private String billtypeNumber;

    /**
     * 业务类型.编码 (固定值，传"310")
     */
    @JsonProperty("biztype_number")
    private String biztypeNumber;

    /**
     * 库存事务.编码 (固定值，传"316")
     */
    @JsonProperty("invscheme_number")
    private String invschemeNumber;

    /**
     * 调拨类型 (固定值，传"A")
     * A:组织内调拨, B:跨组织调拨
     */
    @JsonProperty("transtype")
    private String transtype;

    /**
     * 在途归属 (固定值，传"A")
     * A:调出货主, B:调入货主
     */
    @JsonProperty("transit")
    private String transit;

    /**
     * 调出组织.编码 (固定值，传"BU-002")
     */
    @JsonProperty("outorg_number")
    private String outorgNumber;

    /**
     * 本位币.货币代码 (固定值，传"CNY")
     */
    @JsonProperty("settlescurrency_number")
    private String settlescurrencyNumber;

    /**
     * 备注
     */
    @JsonProperty("comment")
    private String comment;

    /**
     * 物料明细
     */
    @JsonProperty("billentry")
    private List<TransInBillEntry> billentry;
}
