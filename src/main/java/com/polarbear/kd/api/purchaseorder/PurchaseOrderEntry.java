package com.polarbear.kd.api.purchaseorder;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 采购订单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@Data
public class PurchaseOrderEntry {
    
    /**
     * 物料明细.id (修改时必传)
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料编码.编码
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 采购单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 物料明细.单价
     */
    @JsonProperty("price")
    private BigDecimal price;
    
    /**
     * 物料明细.含税单价
     */
    @JsonProperty("priceandtax")
    private BigDecimal priceandtax;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 物料明细.折扣方式 A:折扣率(%), B:单位折扣额, NULL:无
     */
    @JsonProperty("discounttype")
    private String discounttype;
    
    /**
     * 物料明细.单位折扣(率) (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountrate")
    private BigDecimal discountrate;
    
    /**
     * 物料明细.折扣额 (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountamount")
    private BigDecimal discountamount;
    
    /**
     * 税率.编码 (广交云做映射，传对应编码)
     */
    @JsonProperty("taxrateid_number")
    private String taxrateidNumber;
    
    /**
     * 需求组织.编码 (固定值，传"1011")
     */
    @JsonProperty("entryreqorg_number")
    private String entryreqorgNumber;
    
    /**
     * 收料组织.编码 (固定值，传"1011")
     */
    @JsonProperty("entryrecorg_number")
    private String entryrecorgNumber;
    
    /**
     * 结算组织.编码 (固定值，传"1011")
     */
    @JsonProperty("entrysettleorg_number")
    private String entrysettleorgNumber;
    
    /**
     * 物料明细.货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 (固定值，传"bos_org")
     */
    @JsonProperty("ownertype")
    private String ownertype;
    
    /**
     * 货主.编码 (固定值，传"1011")
     */
    @JsonProperty("owner_number")
    private String ownerNumber;
    
    /**
     * 物料明细.承诺日期
     */
    @JsonProperty("promisedate")
    private String promisedate;
    
    /**
     * 物料明细.交货日期
     */
    @JsonProperty("deliverdate")
    private String deliverdate;
    
    /**
     * 物料明细.赠品
     */
    @JsonProperty("ispresent")
    private Boolean ispresent;
    
    /**
     * 项目编码.项目编码
     */
    @JsonProperty("project_number")
    private String projectNumber;
    
    /**
     * 物料明细.备注
     */
    @JsonProperty("entrycomment")
    private String entrycomment;
    
    /**
     * 物料明细.税额
     */
    @JsonProperty("taxamount")
    private BigDecimal taxamount;
    
    /**
     * 物料明细.价税合计
     */
    @JsonProperty("amountandtax")
    private BigDecimal amountandtax;
    
    /**
     * 物料明细.金额
     */
    @JsonProperty("amount")
    private BigDecimal amount;
}
