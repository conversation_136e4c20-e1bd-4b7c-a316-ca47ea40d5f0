package com.polarbear.kd.api.purchaseorder;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 采购订单付款计划实体类
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@Data
public class PurchaseOrderPayEntry {
    
    /**
     * 付款计划.id
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 结算组织.编码 (固定值，传"1011")
     */
    @JsonProperty("planentrysettleorg_number")
    private String planentrysettleorgNumber;
    
    /**
     * 付款计划.到期日
     */
    @JsonProperty("paydate")
    private String paydate;
    
    /**
     * 付款计划.应付比例(%)
     */
    @JsonProperty("payrate")
    private BigDecimal payrate;
    
    /**
     * 付款计划.应付金额
     */
    @JsonProperty("payamount")
    private BigDecimal payamount;
    
    /**
     * 付款计划.是否预付
     */
    @JsonProperty("isprepay")
    private Boolean isprepay;
}
