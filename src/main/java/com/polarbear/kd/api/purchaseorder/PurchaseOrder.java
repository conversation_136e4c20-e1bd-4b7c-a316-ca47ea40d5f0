package com.polarbear.kd.api.purchaseorder;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 采购订单实体类
 * 
 * <AUTHOR>
 * @apiNote 广交云.【采购订单】.新增/修改操作.审核后 推送 金蝶云·星空旗舰版.【采购订单】
 * @date 2025-09-17
 */
@Data
public class PurchaseOrder {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;
    
    /**
     * 外部系统单号
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;
    
    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
    
    /**
     * 采购组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 单据类型.编码 (固定值，传"pm_PurOrderBill_STD_BT_S")
     */
    @JsonProperty("billtype_number")
    private String billtypeNumber;
    
    /**
     * 业务类型.编码 (固定值，传"110")
     */
    @JsonProperty("biztype_number")
    private String biztypeNumber;
    
    /**
     * 采购部门.编码 (固定值，传"GJEY1102")
     */
    @JsonProperty("dept_number")
    private String deptNumber;
    
    /**
     * 订单日期
     */
    @JsonProperty("biztime")
    private String biztime;
    
    /**
     * 订货供应商.编码
     */
    @JsonProperty("supplier_number")
    private String supplierNumber;
    
    /**
     * 付款条件.编号 (对照《付款条件.xlsx》)
     */
    @JsonProperty("paycondition_number")
    private String payconditionNumber;
    
    /**
     * 结算币别.货币代码 (固定值，传"CNY")
     */
    @JsonProperty("settlecurrency_number")
    private String settlecurrencyNumber;
    
    /**
     * 备注
     */
    @JsonProperty("comment")
    private String comment;
    
    /**
     * 含税
     */
    @JsonProperty("istax")
    private Boolean istax;
    
    /**
     * 付款方式 CASH:现购, CREDIT:赊购 (固定值，传"CREDIT")
     */
    @JsonProperty("paymode")
    private String paymode;
    
    /**
     * 汇率日期 (同业务日期)
     */
    @JsonProperty("exratedate")
    private String exratedate;
    
    /**
     * 汇率表.编码 (固定值，传"ERT-01")
     */
    @JsonProperty("exratetable_number")
    private String exratetableNumber;
    
    /**
     * 汇率 (固定值，传1)
     */
    @JsonProperty("exchangerate")
    private BigDecimal exchangerate;
    
    /**
     * 按比例(%)
     */
    @JsonProperty("ispayrate")
    private Boolean ispayrate;
    
    /**
     * 第三方业务编码 (传外部系统单号)
     */
    @JsonProperty("trdbillno")
    private String trdbillno;
    
    /**
     * 物料明细
     */
    @JsonProperty("billentry")
    private List<PurchaseOrderEntry> billentry;
    
    /**
     * 付款计划
     */
    @JsonProperty("purbillentry_pay")
    private List<PurchaseOrderPayEntry> purbillentryPay;
}
