package com.polarbear.kd.api.purchaseorder;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 采购订单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【采购订单】.新增/修改操作.审核后 推送 金蝶云·星空旗舰版.【采购订单】
 * @date 2025-09-17
 */
public class PurchaseOrderSaveRequest extends KdOpRequest<List<PurchaseOrder>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/pm/pm_purorderbill/savePurOrder";
    }

    @Override
    public String logModule() {
        return "kd.pm_purorderbill.savePurOrder";
    }

    @Override
    public Class<PurchaseOrderSaveResponse> getResponseClass() {
        return PurchaseOrderSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<PurchaseOrder>> setLogKey(Config<List<PurchaseOrder>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(PurchaseOrder::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
