package com.polarbear.kd.api.salesorder;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 销售订单实体类
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class SalesOrder {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 订单日期
     */
    @JsonProperty("bizdate")
    private String bizdate;
    
    /**
     * 销售组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 订货客户.编码
     */
    @JsonProperty("customer_number")
    private String customerNumber;
    
    /**
     * 单据类型.编码 (固定值，传"sm_SalesOrder_STD_BT_S")
     */
    @JsonProperty("billtype_number")
    private String billtypeNumber;
    
    /**
     * 业务类型.编码(210-物料类销售) (固定值，传"210")
     */
    @JsonProperty("biztype_number")
    private String biztypeNumber;
    
    /**
     * 结算币别.货币代码 (固定值，传"CNY"(人民币))
     */
    @JsonProperty("settlecurrency_number")
    private String settlecurrencyNumber;
    
    /**
     * 外部系统单号 (传广交云订单编号)
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;
    
    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
    
    /**
     * 表头备注
     */
    @JsonProperty("comment")
    private String comment;
    
    /**
     * 含税
     */
    @JsonProperty("istax")
    private Boolean istax;
    
    /**
     * 录入整单折扣
     */
    @JsonProperty("iswholediscount")
    private Boolean iswholediscount;
    
    /**
     * 整单折扣额
     */
    @JsonProperty("wholediscountamount")
    private String wholediscountamount;

    /**
     * 销售部门.编码 (固定值，传"GJEY1103")
     */
    @JsonProperty("dept_number")
    private String deptNumber;

    /**
     * 汇率日期 (同业务日期)
     */
    @JsonProperty("exratedate")
    private String exratedate;

    /**
     * 汇率表.编码 (固定值，传"ERT-01")
     */
    @JsonProperty("exratetable_number")
    private String exratetableNumber;

    /**
     * 汇率 (固定值，传"1")
     */
    @JsonProperty("exchangerate")
    private String exchangerate;

    /**
     * 收款条件.编号
     * SKTJ-1001_SYS:货到收款
     * SKTJ-1002_SYS:预收30%，货到收款70%
     * SKTJ-1003_SYS:预收30%，开票30天后收款70%
     * SKTJ-1004_SYS:开票30天后收款
     * SKTJ-1005_SYS:开票60天后收款
     * SKTJ-1006_SYS:开票90天后收款
     * SKTJ-1007_SYS:月结30天
     * SKTJ-1008_SYS:按项目计划收款
     */
    @JsonProperty("reccondition_number")
    private String recconditionNumber;
    
    /**
     * 物料明细
     */
    @JsonProperty("billentry")
    private List<SalesOrderEntry> billentry;
    
    public static String SKTJ_1001_SYS = "SKTJ-1001_SYS";
    public static String SKTJ_1002_SYS = "SKTJ-1002_SYS";
    public static String SKTJ_1003_SYS = "SKTJ-1003_SYS";
    public static String SKTJ_1004_SYS = "SKTJ-1004_SYS";
    public static String SKTJ_1005_SYS = "SKTJ-1005_SYS";
    public static String SKTJ_1006_SYS = "SKTJ-1006_SYS";
    public static String SKTJ_1007_SYS = "SKTJ-1007_SYS";
    public static String SKTJ_1008_SYS = "SKTJ-1008_SYS";
}
