package com.polarbear.kd.api.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 物料库存信息更新参数
 *
 * <AUTHOR>
 */
@Data
public class MaterialInventoryUpdatePara {

  /** 启用批号管理 */
  @JsonProperty("enablelot")
  private Boolean enablelot;

  /** 保质期管理 */
  @JsonProperty("enableshelflifemgr")
  private Boolean enableshelflifemgr;

  /** 保质期单位 day:日, month:月, year:年 */
  @JsonProperty("shelflifeunit")
  private String shelflifeunit;

  /** 保质期 */
  @JsonProperty("shelflife")
  private Integer shelflife;

  /** 计算方向 1:按生产日计算到期日, 2:按到期日计算生产日, 3:相互计算, 4:互不计算 - 固定值，传"4" */
  @JsonProperty("caldirection")
  private String caldirection;

  /** 提前期单位 day:日, month:月, year:年 */
  @JsonProperty("leadtimeunit")
  private String leadtimeunit;

  /** 物料编码.编码 */
  @JsonProperty("masterid_number")
  private String masteridNumber;

  /** 库存信息创建组织.编码 - 固定值，传"gjwl" */
  @JsonProperty("createorg_number")
  private String createorgNumber;

  /** 库存单位(编码) */
  @JsonProperty("inventoryunit_number")
  private String inventoryunitNumber;
}
