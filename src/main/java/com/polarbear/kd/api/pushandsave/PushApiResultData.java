package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 下推结果数据
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class PushApiResultData {
    
    /**
     * 下推并保存返回结果详细信息（单个对象，不是数组）
     */
    @JsonProperty("pushResult")
    private PushResult pushResult;
    
    /**
     * 下推并保存返回目标单据详细信息
     */
    @JsonProperty("targetResult")
    private List<TargetResult> targetResult;
}
