package com.polarbear.kd.api.attachment;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarbear.base.api.client.KingdeeApiBaseClient;
import com.polarbear.base.api.client.SdkLogClient;
import com.polarbear.base.api.model.KdAccessTokenDTO;
import com.polarbear.base.api.model.SdkLogDTO;
import com.polarbear.kd.core.Log4Sdk;
import com.polarbear.kd.exception.KingDeeOpException;
import com.polarbear.springframework.boot.common.model.CommonResult;
import com.polarbear.springframework.boot.service.exception.ServiceException;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

/**
 * 附件上传接口
 *
 * <AUTHOR>
 * @apiNote 用于上传附件并绑定附件到对应单据的接口 请求格式为multipart/form-data，包含两个字段： 1. attachmentUploadFileArgs:
 *     JSON字符串格式的附件参数 2. file: 文件对象
 *     <p>注意：此类不继承KdOpRequest，直接实现文件上传逻辑
 * @date 2025-09-02
 */
@Slf4j
public class AttachmentUploadRequest {

  private static final ObjectMapper objectMapper = new ObjectMapper();
  private static final OkHttpClient httpClient =
      new OkHttpClient.Builder()
          .connectTimeout(180, TimeUnit.SECONDS)
          .readTimeout(180, TimeUnit.SECONDS)
          .writeTimeout(180, TimeUnit.SECONDS)
          .build();

  private AttachmentUploadRequestParam param;
  private SdkLogClient sdkLogClient;

  /** 设置请求参数 */
  public AttachmentUploadRequest setParam(AttachmentUploadRequestParam param) {
    this.param = param;
    return this;
  }

  /** 设置日志客户端 */
  public AttachmentUploadRequest sdkLogClient(SdkLogClient sdkLogClient) {
    this.sdkLogClient = sdkLogClient;
    return this;
  }

  /** 获取URL路径 */
  public String getUrlPath() {
    return "/v2/frame/attachment/uploadFile";
  }

  /** 获取日志模块 */
  public String logModule() {
    return "kd.frame.attachment.uploadFile";
  }

  /** 执行文件上传（通过KingdeeApiBaseClient获取Token） */
  public AttachmentUploadResponse execute(KingdeeApiBaseClient api) {
    return execute(api, "");
  }

  /** 执行文件上传（通过KingdeeApiBaseClient获取Token，指定租户ID） */
  public AttachmentUploadResponse execute(KingdeeApiBaseClient api, String tenantId) {
    CommonResult<KdAccessTokenDTO> accessTokenResult = api.getAccessToken(tenantId);
    ServiceException.check(accessTokenResult);
    return execute(accessTokenResult.getData());
  }

  /** 执行文件上传（直接使用AccessToken） */
  public AttachmentUploadResponse execute(KdAccessTokenDTO accessToken) {
    if (param == null) {
      throw new ServiceException("请求参数不能为空");
    }
    if (param.getFile() == null || param.getFile().length == 0) {
      throw new ServiceException("文件内容不能为空");
    }
    if (param.getFileName() == null || param.getFileName().trim().isEmpty()) {
      throw new ServiceException("文件名不能为空");
    }
    if (param.getAttachmentUploadFileArgs() == null) {
      throw new ServiceException("附件上传参数不能为空");
    }

    String logKey = generateLogKey();
    Log4Sdk.SdkLogBuilder logBuilder =
        Log4Sdk.builder(objectMapper).module(logModule()).request(param);

    try {
      String responseContent = uploadFile(accessToken, logBuilder);
      if (responseContent == null) {
        return null; // 租户未使用金蝶系统
      }

      // 解析响应
      AttachmentUploadResponse response =
          objectMapper.readValue(responseContent, AttachmentUploadResponse.class);
      response.setOriginalResponse(responseContent);
      logBuilder.response(responseContent);

      // 检查业务响应码
      response.check();

      return response;
    } catch (Exception ex) {
      logBuilder.error(ex);
      if (ex instanceof KingDeeOpException) {
        throw (KingDeeOpException) ex;
      }
      throw new KingDeeOpException(KingDeeOpException.Code.UNRECOGNIZED_EXCEPTION, ex.getMessage());
    } finally {
      saveLog(logBuilder, logKey);
    }
  }

  /** 执行文件上传的核心逻辑 */
  private String uploadFile(KdAccessTokenDTO accessToken, Log4Sdk.SdkLogBuilder logBuilder)
      throws IOException {
    if (accessToken == null) {
      throw new ServiceException("accessToken is null");
    }
    if (!accessToken.getNeedCall()) {
      // 租户未使用金蝶系统，不需要调用API
      return null;
    }
    if (accessToken.getAccessToken() == null) {
      throw new KingDeeOpException(
          KingDeeOpException.Code.TOKEN_CHECK_SIGN_ERROR,
          "can not get accessToken, please check your access token or refresh token");
    }

    // 构建服务器URL
    String host = accessToken.getServerUrl();
    if (host.endsWith("/")) {
      host = host.substring(0, host.length() - 1);
    }
    String url = host + "/kapi" + getUrlPath();

    // 将附件参数转换为JSON字符串
    String attachmentArgsJson =
        objectMapper.writeValueAsString(param.getAttachmentUploadFileArgs());
    logBuilder.requestContent(
        "attachmentUploadFileArgs: "
            + attachmentArgsJson
            + ", fileName: "
            + param.getFileName()
            + ", fileSize: "
            + param.getFile().length
            + " bytes");

    // 构建multipart/form-data请求体
    String contentType =
        param.getContentType() != null ? param.getContentType() : "application/octet-stream";
    MultipartBody.Builder multipartBuilder =
        new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("attachmentUploadFileArgs", attachmentArgsJson)
            .addFormDataPart(
                "file",
                param.getFileName(),
                RequestBody.create(MediaType.parse(contentType), param.getFile()));

    RequestBody requestBody = multipartBuilder.build();

    // 构建HTTP请求
    Request request =
        new Request.Builder()
            .url(url)
            .header("accesstoken", accessToken.getAccessToken())
            .header("x-acgw-identity", accessToken.getXAcgwIdentity())
            .header("Idempotency-Key", UUID.randomUUID().toString())
            .post(requestBody)
            .build();

    // 执行请求
    try (Response response = httpClient.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        handleHttpError(response);
      }

      String responseContent = response.body() == null ? "{}" : response.body().string();
      return responseContent;
    }
  }

  /** 处理HTTP错误响应 */
  private void handleHttpError(Response response) throws IOException {
    String errorMessage = "HTTP请求失败，状态码: " + response.code();
    if (response.body() != null) {
      errorMessage += ", 响应内容: " + response.body().string();
    }

    switch (response.code()) {
      case 400:
        throw new KingDeeOpException(KingDeeOpException.Code.PARAMETER_CHECK_ERROR, errorMessage);
      case 401:
        throw new KingDeeOpException(KingDeeOpException.Code.TOKEN_CHECK_SIGN_ERROR, errorMessage);
      case 403:
        throw new KingDeeOpException(KingDeeOpException.Code.Forbidden_ERROR, errorMessage);
      case 404:
        throw new KingDeeOpException(KingDeeOpException.Code.API_ERROR, errorMessage);
      default:
        throw new KingDeeOpException(KingDeeOpException.Code.HTTP_REQUEST_ERROR, errorMessage);
    }
  }

  /** 生成日志键值 */
  private String generateLogKey() {
    if (param != null && param.getAttachmentUploadFileArgs() != null) {
      AttachmentUploadFileArgs args = param.getAttachmentUploadFileArgs();
      return args.getEntityNumber() + "_" + args.getBillPkId() + "_" + args.getControlKey();
    }
    return "unknown";
  }

  /** 保存日志 */
  private void saveLog(Log4Sdk.SdkLogBuilder logBuilder, String logKey) {
    try {
      SdkLogDTO log = logBuilder.build();
      if (logKey != null) {
        log.setKey1(logKey);
      }
      if (sdkLogClient != null) {
        sdkLogClient.saveLog(log);
      }
    } catch (Exception e) {
      log.error("保存附件上传请求日志异常", e);
    }
  }

  /**
   * 创建附件上传请求参数（从文件对象）
   *
   * @param args 附件上传参数
   * @param file 文件对象
   * @return 请求参数对象
   * @throws IOException 读取文件失败时抛出
   */
  public static AttachmentUploadRequestParam createRequest(AttachmentUploadFileArgs args, File file)
      throws IOException {
    if (file == null || !file.exists()) {
      throw new IllegalArgumentException("文件不存在");
    }

    AttachmentUploadRequestParam param = new AttachmentUploadRequestParam();
    param.setAttachmentUploadFileArgs(args);
    param.setFile(Files.readAllBytes(file.toPath()));
    param.setFileName(file.getName());

    // 根据文件扩展名推断内容类型
    String contentType = getContentTypeByFileName(file.getName());
    param.setContentType(contentType);

    return param;
  }

  /**
   * 创建附件上传请求参数（从字节数组）
   *
   * @param args 附件上传参数
   * @param fileBytes 文件字节数组
   * @param fileName 文件名
   * @return 请求参数对象
   */
  public static AttachmentUploadRequestParam createRequest(
      AttachmentUploadFileArgs args, byte[] fileBytes, String fileName) {
    return createRequest(args, fileBytes, fileName, null);
  }

  /**
   * 创建附件上传请求参数（从字节数组，指定内容类型）
   *
   * @param args 附件上传参数
   * @param fileBytes 文件字节数组
   * @param fileName 文件名
   * @param contentType 内容类型
   * @return 请求参数对象
   */
  public static AttachmentUploadRequestParam createRequest(
      AttachmentUploadFileArgs args, byte[] fileBytes, String fileName, String contentType) {
    if (fileBytes == null || fileBytes.length == 0) {
      throw new IllegalArgumentException("文件内容不能为空");
    }
    if (fileName == null || fileName.trim().isEmpty()) {
      throw new IllegalArgumentException("文件名不能为空");
    }

    AttachmentUploadRequestParam param = new AttachmentUploadRequestParam();
    param.setAttachmentUploadFileArgs(args);
    param.setFile(fileBytes);
    param.setFileName(fileName);
    param.setContentType(contentType != null ? contentType : getContentTypeByFileName(fileName));

    return param;
  }

  /**
   * 根据文件名推断内容类型
   *
   * @param fileName 文件名
   * @return 内容类型
   */
  private static String getContentTypeByFileName(String fileName) {
    if (fileName == null) {
      return "application/octet-stream";
    }

    String lowerFileName = fileName.toLowerCase();
    if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
      return "image/jpeg";
    } else if (lowerFileName.endsWith(".png")) {
      return "image/png";
    } else if (lowerFileName.endsWith(".gif")) {
      return "image/gif";
    } else if (lowerFileName.endsWith(".pdf")) {
      return "application/pdf";
    } else if (lowerFileName.endsWith(".txt")) {
      return "text/plain";
    } else if (lowerFileName.endsWith(".doc")) {
      return "application/msword";
    } else if (lowerFileName.endsWith(".docx")) {
      return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    } else if (lowerFileName.endsWith(".xls")) {
      return "application/vnd.ms-excel";
    } else if (lowerFileName.endsWith(".xlsx")) {
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    } else {
      return "application/octet-stream";
    }
  }

  /**
   * 将AttachmentUploadFileArgs转换为JSON字符串 用于multipart/form-data中的文本字段
   *
   * @param args 附件上传参数
   * @return JSON字符串
   * @throws RuntimeException 转换失败时抛出
   */
  public static String toJsonString(AttachmentUploadFileArgs args) {
    try {
      return objectMapper.writeValueAsString(args);
    } catch (Exception e) {
      throw new RuntimeException("转换AttachmentUploadFileArgs为JSON字符串失败", e);
    }
  }
}
