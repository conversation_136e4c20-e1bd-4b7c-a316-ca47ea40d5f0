package com.polarbear.kd.api.purinbill;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 采购入库退料单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【采购入库单】.修改操作.审核后 推送 金蝶云·星空旗舰版.【采购入库单】
 *          广交云.【采退出库单】.修改操作.审核后 推送 金蝶云·星空旗舰版.【采购退料单】
 *          采购入库单与采购退料单保存接口相同，通过单据类型编码区分
 * @date 2025-09-19
 */
public class PurInBillSaveRequest extends KdOpRequest<List<PurInBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/im_purinbill/savePruinBill";
    }

    @Override
    public String logModule() {
        return "kd.im_purinbill.savePruinBill";
    }

    @Override
    public Class<PurInBillSaveResponse> getResponseClass() {
        return PurInBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<PurInBill>> setLogKey(Config<List<PurInBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(PurInBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
