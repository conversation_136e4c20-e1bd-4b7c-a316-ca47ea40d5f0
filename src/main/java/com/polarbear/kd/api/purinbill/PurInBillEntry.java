package com.polarbear.kd.api.purinbill;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购入库退料单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-19
 */
@Data
public class PurInBillEntry {
    
    /**
     * 物料明细.id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 物料明细.退料类型 (如为采购退料单时传"1")
     * 0: 空, 1: 退料, 2: 退补料
     */
    @JsonProperty("returnmaterialtype")
    private String returnmaterialtype;
    
    /**
     * 库存单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 入库库存状态.编码 (固定值，传"110")
     */
    @JsonProperty("invstatus_number")
    private String invstatusNumber;
    
    /**
     * 物料明细.入库货主类型 (固定值，传"bos_org")
     * bd_supplier:供应商, bd_customer:客户, bos_org:核算组织
     */
    @JsonProperty("ownertype")
    private String ownertype;
    
    /**
     * 入库货主.编码 (固定值，传"1011")
     */
    @JsonProperty("owner_number")
    private String ownerNumber;
    
    /**
     * 物料明细.入库保管者类型 (固定值，传"bos_org")
     * bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("keepertype")
    private String keepertype;
    
    /**
     * 入库保管者.编码 (固定值，传"1011")
     */
    @JsonProperty("keeper_number")
    private String keeperNumber;
    
    /**
     * 入库库存类型.编码 (固定值，传"110")
     */
    @JsonProperty("invtype_number")
    private String invtypeNumber;
    
    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 物料编码.编码 (广交产品编码)
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 基本单位.编码 (同库存单位.编码)
     */
    @JsonProperty("baseunit_number")
    private String baseunitNumber;
    
    /**
     * 物料明细.基本数量 (同物料明细.数量)
     */
    @JsonProperty("baseqty")
    private BigDecimal baseqty;
    
    /**
     * 税率.编码
     */
    @JsonProperty("taxrateid_number")
    private String taxrateidNumber;
    
    /**
     * 物料明细.税额
     */
    @JsonProperty("taxamount")
    private BigDecimal taxamount;
    
    /**
     * 物料明细.价税合计
     */
    @JsonProperty("amountandtax")
    private BigDecimal amountandtax;
    
    /**
     * 物料明细.金额
     */
    @JsonProperty("amount")
    private BigDecimal amount;
    
    /**
     * 物料明细.赠品 (默认为false，金额为0传true)
     */
    @JsonProperty("ispresent")
    private Boolean ispresent;
    
    /**
     * 物料明细.折扣方式
     * A:折扣率(%), NULL:无, B:单位折扣额
     */
    @JsonProperty("discounttype")
    private String discounttype;
    
    /**
     * 物料明细.单位折扣(率) (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountrate")
    private BigDecimal discountrate;
    
    /**
     * 物料明细.折扣额 (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountamount")
    private BigDecimal discountamount;
    
    /**
     * 物料明细.备注
     */
    @JsonProperty("entrycomment")
    private String entrycomment;
    
    /**
     * 项目编码.项目编码
     */
    @JsonProperty("project_number")
    private String projectNumber;
    
    /**
     * 物料明细.生产日期 (物料启用保质期时必传)
     */
    @JsonProperty("producedate")
    private String producedate;
    
    /**
     * 物料明细.到期日期 (物料启用保质期时必传)
     */
    @JsonProperty("expirydate")
    private String expirydate;
    
    /**
     * 物料明细.批号 (对应广交云批次，物料启用批号时必传)
     */
    @JsonProperty("lotnumber")
    private String lotnumber;
    
    /**
     * 物料明细.生产批号 (对应广交云生产批号)
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
    
    /**
     * 单价
     */
    @JsonProperty("price")
    private BigDecimal price;
    
    /**
     * 含税单价
     */
    @JsonProperty("priceandtax")
    private BigDecimal priceandtax;
    
    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;
    
    // 常量定义
    
    /**
     * 退料类型 - 退料
     */
    public static final String RETURN_MATERIAL_TYPE_RETURN = "1";
    
    /**
     * 退料类型 - 退补料
     */
    public static final String RETURN_MATERIAL_TYPE_RETURN_SUPPLEMENT = "2";
    
    /**
     * 入库库存状态 - 可用
     */
    public static final String INVSTATUS_AVAILABLE = "110";
    
    /**
     * 货主类型 - 核算组织
     */
    public static final String OWNER_TYPE_ORG = "bos_org";
    
    /**
     * 保管者类型 - 库存组织
     */
    public static final String KEEPER_TYPE_ORG = "bos_org";
    
    /**
     * 入库库存类型 - 普通
     */
    public static final String INVTYPE_NORMAL = "110";
    
    /**
     * 行类型 - 物资
     */
    public static final String LINETYPE_MATERIAL = "010";
    
    /**
     * 折扣方式 - 折扣率(%)
     */
    public static final String DISCOUNT_TYPE_RATE = "A";
    
    /**
     * 折扣方式 - 无
     */
    public static final String DISCOUNT_TYPE_NULL = "NULL";
    
    /**
     * 折扣方式 - 单位折扣额
     */
    public static final String DISCOUNT_TYPE_AMOUNT = "B";
    
    /**
     * 默认组织编码
     */
    public static final String DEFAULT_ORG = "1011";
}
