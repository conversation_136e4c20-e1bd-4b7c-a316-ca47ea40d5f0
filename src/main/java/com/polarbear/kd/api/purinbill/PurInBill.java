package com.polarbear.kd.api.purinbill;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购入库退料单实体类
 * 
 * <AUTHOR>
 * @apiNote 支持采购入库单和采购退料单两种类型，通过单据类型编码区分：
 *          采购入库单：im_PurInBill_STD_BT_S
 *          采购退料单：im_PurInBill_STD_BT_S_R
 * @date 2025-09-19
 */
@Data
public class PurInBill {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 库存组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;
    
    /**
     * 外部系统单号 (传广交云单号)
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;
    
    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
    
    /**
     * 单据类型.编码
     * 采购入库单：im_PurInBill_STD_BT_S
     * 采购退料单：im_PurInBill_STD_BT_S_R
     */
    @JsonProperty("billtype_number")
    private String billtypeNumber;
    
    /**
     * 业务日期
     */
    @JsonProperty("biztime")
    private String biztime;
    
    /**
     * 汇率日期 (同业务日期)
     */
    @JsonProperty("exratedate")
    private String exratedate;
    
    /**
     * 汇率表.编码 (固定值，传"ERT-01")
     */
    @JsonProperty("exratetable_number")
    private String exratetableNumber;
    
    /**
     * 汇率 (固定值，传1)
     */
    @JsonProperty("exchangerate")
    private BigDecimal exchangerate;
    
    /**
     * 付款方式
     */
    @JsonProperty("paymode")
    private String paymode;
    
    /**
     * 采购组织.编码 (固定值，传"1011")
     */
    @JsonProperty("bizorg_number")
    private String bizorgNumber;
    
    /**
     * 业务类型.编码
     * 如为采购入库单时传"110"
     * 如为采购退料单时传"1101"
     */
    @JsonProperty("biztype_number")
    private String biztypeNumber;
    
    /**
     * 库存事务.编码
     * 如为采购入库单时传"110"
     * 如为采购退料单时传"1101"
     */
    @JsonProperty("invscheme_number")
    private String invschemeNumber;
    
    /**
     * 供应商.编码
     */
    @JsonProperty("supplier_number")
    private String supplierNumber;
    
    /**
     * 本位币.货币代码 (固定值，传"CNY"(人民币))
     */
    @JsonProperty("currency_number")
    private String currencyNumber;
    
    /**
     * 结算币别.货币代码 (固定值，传"CNY"(人民币))
     */
    @JsonProperty("settlecurrency_number")
    private String settlecurrencyNumber;
    
    /**
     * 付款条件.编号
     */
    @JsonProperty("paycondition_number")
    private String payconditionNumber;
    
    /**
     * 备注
     */
    @JsonProperty("comment")
    private String comment;
    
    /**
     * 记账日期
     */
    @JsonProperty("bookdate")
    private String bookdate;
    
    /**
     * 含税标识
     */
    @JsonProperty("istax")
    private Boolean istax;
    
    /**
     * 物料明细
     */
    @JsonProperty("billentry")
    private List<PurInBillEntry> billentry;
    
    // 常量定义
    
    /**
     * 采购入库单单据类型编码
     */
    public static final String BILLTYPE_PUR_IN = "im_PurInBill_STD_BT_S";
    
    /**
     * 采购退料单单据类型编码
     */
    public static final String BILLTYPE_PUR_RETURN = "im_PurInBill_STD_BT_S_R";
    
    /**
     * 采购入库业务类型编码
     */
    public static final String BIZTYPE_PUR_IN = "110";
    
    /**
     * 采购退料业务类型编码
     */
    public static final String BIZTYPE_PUR_RETURN = "1101";
    
    /**
     * 采购入库库存事务编码
     */
    public static final String INVSCHEME_PUR_IN = "110";
    
    /**
     * 采购退料库存事务编码
     */
    public static final String INVSCHEME_PUR_RETURN = "1101";
    
    /**
     * 付款方式 - 赊购
     */
    public static final String PAYMODE_CREDIT = "CREDIT";
    
    /**
     * 货币代码 - 人民币
     */
    public static final String CURRENCY_CNY = "CNY";
    
    /**
     * 汇率表编码
     */
    public static final String EXRATETABLE_DEFAULT = "ERT-01";
    
    /**
     * 默认组织编码
     */
    public static final String ORG_DEFAULT = "1011";
    
    /**
     * 来源系统
     */
    public static final String SOURCE_SYSTEM = "广交云供应链管理系统";
}
