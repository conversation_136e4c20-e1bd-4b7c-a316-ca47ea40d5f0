package com.polarbear.kd.core;


import com.polarbear.base.api.client.KingdeeApiBaseClient;
import com.polarbear.base.api.client.SdkLogClient;
import com.polarbear.base.api.model.KdAccessTokenDTO;
import com.polarbear.kd.api.EmptyResponse;
import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.exception.KingDeeOpException;
import com.polarbear.springframework.boot.common.model.CommonResult;
import com.polarbear.springframework.boot.service.exception.ServiceException;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.Constructor;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求基类，
 * 所有请求类都继承此类
 *
 * <AUTHOR>
 * @date 2024/10/12
 */
public abstract class KdOpRequest<T> implements KdOpRequestExecutor {

    private static final KingdeeDefaultOpClient client = KingdeeDefaultOpClient.instance();
    private static Boolean registered = false;

    public T getParam() {
        return param;
    }

    /**
     * 设置请求参数
     *
     * @param param 请求参数
     */
    public KdOpRequest<T> setParam(T param) {
        this.param = param;
        return this;
    }

    private T param;

    private SdkLogClient sdkLogClient;

    public KdOpRequest<T> sdkLogClient(SdkLogClient sdkLogClient) {
        this.sdkLogClient = sdkLogClient;
        return this;
    }

    SdkLogClient getSdkLogClient() {
        return sdkLogClient;
    }

    public boolean standard() {
        return true;
    }

    /**
     * 接口请求地址
     */
    public abstract String getUrlPath();

    /**
     * 接口请求地址
     */
    public abstract String logModule();

    public KdOpRequest<T> setLogKey(Config<T> config) {
        return this;
    }

    private Object getRequestParam() {
        return this.standard() ? new KingdeeDefaultOpClient.RequestData<>(this.getParam()) : this.getParam();
    }

    @SuppressWarnings("unchecked")
    public KdOpRequest() {
        try {
            Type actualTypeArgument = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
            if (actualTypeArgument instanceof Class) {
                Class<?> paramClass = (Class<?>) actualTypeArgument;
                this.param = (T) paramClass.newInstance();
            } else if (actualTypeArgument instanceof ParameterizedType &&
                    (((ParameterizedTypeImpl) actualTypeArgument).getRawType()).isAssignableFrom(List.class)) {
                Constructor<?> constructor = ArrayList.class.getDeclaredConstructor();
                this.param = (T) constructor.newInstance();
            }
        } catch (Exception exception) {
            throw new KingDeeOpException(KingDeeOpException.Code.INIT_REQUEST_PARAM_ERROR, exception);
        }
        if (!registered) {
            if (Register.getConfigBuilder(logModule()) == null) {
                Config<T> config = new Config<T>().setModule(this.logModule());
                setLogKey(config);
                Register.register(config);
                registered = true;
            }
        }
    }

    @Override
    public String request(KingdeeApiBaseClient api) {
        return client.requestContent(this.getUrlPath(), this.logModule(), this.getRequestParam(), api, sdkLogClient);
    }

    @Override
    public String request(KdAccessTokenDTO accessToken) {
        return client.requestContent(this.getUrlPath(), this.logModule(), this.getRequestParam(), accessToken, sdkLogClient);
    }

    @Override
    public <R> R execute(KdAccessTokenDTO accessToken) {
        KdResponseWrapper<R> responseWrapper = client.request(this, accessToken, standard());
        if (responseWrapper.getResponse() instanceof KdResponse<?>) {
            KdResponse<?> response = (KdResponse<?>) responseWrapper.getResponse();
            switch (response.getErrorCode()) {
                case "999":
                    throw new KingDeeOpException(KingDeeOpException.Code.UNRECOGNIZED_EXCEPTION, response.getMessage());
                case "400":
                    throw new KingDeeOpException(KingDeeOpException.Code.PARAMETER_CHECK_ERROR, response.getMessage());
                case "401":
                    throw new KingDeeOpException(KingDeeOpException.Code.TOKEN_CHECK_SIGN_ERROR, response.getMessage());
                case "403":
                    throw new KingDeeOpException(KingDeeOpException.Code.Forbidden_ERROR, response.getMessage());
                case "404":
                    throw new KingDeeOpException(KingDeeOpException.Code.API_ERROR, response.getMessage());
                case "601":
                    throw new KingDeeOpException(KingDeeOpException.Code.DATA_DUPLICATE_ERROR, response.getMessage());
                case "602":
                    throw new KingDeeOpException(KingDeeOpException.Code.NOT_FOUND_DATA_ERROR, response.getMessage());
//                case "603":
//                    throw new KingDeeOpException(KingDeeOpException.Code.DATA_INVALID_ERROR, response.getMessage());
                case "611":
                    throw new KingDeeOpException(KingDeeOpException.Code.UPDATE_DATA_FAIL_ERROR, response.getMessage());
                case "612":
                    throw new KingDeeOpException(KingDeeOpException.Code.NO_DATA_OPERATION_PERMISSION_ERROR, response.getMessage());
                case "701":
                    throw new KingDeeOpException(KingDeeOpException.Code.SCRIPT_EXECUTE_ERROR, response.getMessage());
                case "702":
                    throw new KingDeeOpException(KingDeeOpException.Code.PLUGIN_EXECUTE_ERROR, response.getMessage());
            }
        }
        return responseWrapper.getResponse();
    }

    @Override
    public <R> R execute(KingdeeApiBaseClient api) {
        return execute(api, "");
    }

    @Override
    public <R> R execute(KingdeeApiBaseClient api, String tenantId) {
        CommonResult<KdAccessTokenDTO> accessToken = api.getAccessToken(tenantId);
        ServiceException.check(accessToken);
        return execute(accessToken.getData());
    }

    public KingdeeDefaultOpClient getClient() {
        return client;
    }

    public abstract Class<? extends EmptyResponse> getResponseClass();

}
