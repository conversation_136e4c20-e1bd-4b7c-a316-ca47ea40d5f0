package com.polarbear.kd.core;

import com.polarbear.base.api.client.KingdeeApiBaseClient;
import com.polarbear.base.api.model.KdAccessTokenDTO;

public interface KdOpRequestExecutor {
    String request(KingdeeApiBaseClient api);

    String request(KdAccessTokenDTO accessToken);

    <R> R execute(KdAccessTokenDTO accessToken);

    <R> R execute(KingdeeApiBaseClient api);

    <R> R execute(KingdeeApiBaseClient api, String tenantId);
}
